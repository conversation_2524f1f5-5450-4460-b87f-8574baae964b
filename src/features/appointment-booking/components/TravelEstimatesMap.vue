<template>
    <div class="travel-estimates-map">
        <Card v-if="filteredEstimates.length > 0" class="p-card">
            <template #title>Travel Estimates</template>
            <template #content>
                <div v-if="totalTravelSummary" class="total-summary p-mb-3 flex align-items-center">
                    <i class="pi pi-car p-mr-2" style="font-size: 1.5rem"></i>
                    <span class="font-semibold">Total Journey:</span>
                    <span class="p-ml-2">{{ totalTravelSummary.duration }} ({{ totalTravelSummary.distance }})</span>
                </div>
                <div class="p-grid p-nogutter p-gap-3">
                    <div v-for="(estimate, index) in filteredEstimates" :key="index" class="p-col-12 p-md-6">
                        <Fieldset :legend="`Estimate ${index + 1}`" :toggleable="true">
                            <div class="p-field flex align-items-center mb-2">
                                <i class="pi pi-map-marker mr-2 text-primary"></i>
                                <span class="font-semibold">From:</span> <span class="ml-2">{{ estimate.from }}</span>
                            </div>
                            <div class="p-field flex align-items-center mb-2">
                                <i class="pi pi-flag-fill mr-2 text-primary"></i>
                                <span class="font-semibold">To:</span> <span class="ml-2">{{ estimate.to }}</span>
                            </div>
                            <div class="p-field flex align-items-center mb-2">
                                <i class="pi pi-clock mr-2 text-primary"></i>
                                <span class="font-semibold">Duration:</span> <span class="ml-2">{{ estimate.durationMinutes }} minutes</span>
                            </div>
                            <div class="p-field flex align-items-center">
                                <i class="pi pi-globe mr-2 text-primary"></i>
                                <span class="font-semibold">Distance:</span> <span class="ml-2">{{ estimate.distanceKm }} km</span>
                            </div>
                        </Fieldset>
                    </div>
                </div>
                <div class="map-container">
                    <!-- Google Map will be rendered here -->
                    <div id="map" style="width: 100%; height: 400px"></div>
                </div>
            </template>
        </Card>
        <Message v-else severity="info" :closable="false"> No travel estimates available. </Message>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { loader } from '@/features/appointment-booking/services/appointmentService';

interface TravelEstimate {
    from: string;
    to: string;
    durationMinutes: number;
    distanceKm: number;
    fromCoordinates: [number, number];
    toCoordinates: [number, number];
}

const props = defineProps<{
    travelEstimate: TravelEstimate[];
}>();

const map = ref<google.maps.Map | null>(null);
const directionsService = ref<google.maps.DirectionsService | null>(null);
const directionsRenderers = ref<google.maps.DirectionsRenderer[]>([]);
const infoWindow = ref<google.maps.InfoWindow | null>(null);
const customMarkers = ref<google.maps.Marker[]>([]);

interface RouteLegData {
    duration: google.maps.Duration;
    distance: google.maps.Distance;
    duration_in_traffic?: google.maps.Duration;
}
const routeLegsData = ref<RouteLegData[]>([]);

const filteredEstimates = computed(() => {
    if (!props.travelEstimate || props.travelEstimate.length === 0) {
        return [];
    }

    const processedDestinations: Set<string> = new Set();
    const processedRoutes: Set<string> = new Set();
    const result: TravelEstimate[] = [];

    props.travelEstimate.forEach((estimate) => {
        const fromCoordString = `${estimate.fromCoordinates[0]},${estimate.fromCoordinates[1]}`;
        const toCoordString = `${estimate.toCoordinates[0]},${estimate.toCoordinates[1]}`;
        const routeString = `${fromCoordString}-${toCoordString}`;

        // New filter: Remove zero-length segments (origin == destination)
        if (fromCoordString === toCoordString) {
            console.warn(`Filtering out zero-length estimate: ${estimate.from} to ${estimate.to} (Origin and Destination are the same).`);
            return; // Skip this estimate
        }

        // 1. Check for exact duplicate routes (same origin and destination)
        if (processedRoutes.has(routeString)) {
            console.warn(`Filtering out redundant exact duplicate route: ${estimate.from} to ${estimate.to}`);
            return; // Skip this estimate
        }

        // 2. Check if the destination has already been visited by a previous route
        if (processedDestinations.has(toCoordString)) {
            console.warn(`Filtering out estimate: ${estimate.from} to ${estimate.to} as its destination has already been visited by a previous route.`);
            return; // Skip this estimate
        }

        // If the estimate passes all checks, add it to the processed sets and result
        processedRoutes.add(routeString);
        processedDestinations.add(toCoordString);
        result.push(estimate);
    });
    return result;
});

const totalTravelSummary = computed(() => {
    if (routeLegsData.value.length === 0) {
        return null;
    }

    let totalDurationSeconds = 0;
    let totalDistanceMeters = 0;

    routeLegsData.value.forEach((legData) => {
        // Prioritize traffic duration if available, otherwise use regular duration
        totalDurationSeconds += legData.duration_in_traffic?.value || legData.duration.value;
        totalDistanceMeters += legData.distance.value;
    });

    // Format duration
    const hours = Math.floor(totalDurationSeconds / 3600);
    const minutes = Math.floor((totalDurationSeconds % 3600) / 60);
    let durationText = '';
    if (hours > 0) {
        durationText += `${hours} hr `;
    }
    durationText += `${minutes} min`;

    // Format distance (convert meters to miles)
    const totalDistanceMiles = (totalDistanceMeters * 0.*********).toFixed(1); // 1 decimal place

    return {
        duration: durationText,
        distance: `${totalDistanceMiles} miles`
    };
});

// Function to initialize Google Map
const initMap = () => {
    // Check if Google Maps API is already loaded
    const loaderStatus = loader.status;
    if (loaderStatus !== 2) {
        loader.load();
    }

    if (filteredEstimates.value.length === 0 || !window.google) {
        return;
    }

    // Clear existing directions renderers
    directionsRenderers.value.forEach((renderer) => renderer.setMap(null));
    directionsRenderers.value = [];

    // Clear existing custom markers
    customMarkers.value.forEach((marker) => marker.setMap(null));
    customMarkers.value = [];

    // Clear previous route leg data
    routeLegsData.value = [];

    // Initialize InfoWindow if not already done
    if (!infoWindow.value) {
        infoWindow.value = new google.maps.InfoWindow();
    }

    if (!map.value) {
        // Initialize map with the first filtered estimate's origin as center
        const firstEstimate = filteredEstimates.value[0];
        map.value = new google.maps.Map(document.getElementById('map') as HTMLElement, {
            zoom: 10,
            center: { lat: firstEstimate.fromCoordinates[0], lng: firstEstimate.fromCoordinates[1] }
        });
        directionsService.value = new google.maps.DirectionsService();

        // Add Traffic Layer
        new google.maps.TrafficLayer().setMap(map.value);
    }

    let markerCounter = 1;

    // Create marker for the very first origin (Start of the entire journey)
    const firstOrigin = filteredEstimates.value[0].fromCoordinates;
    const originMarker = new google.maps.Marker({
        position: { lat: firstOrigin[0], lng: firstOrigin[1] },
        map: map.value,
        label: {
            text: String(markerCounter++),
            color: '#FFFFFF',
            fontWeight: 'bold'
        },
        title: `Starting Point: ${filteredEstimates.value[0].from}`
    });
    customMarkers.value.push(originMarker);

    filteredEstimates.value.forEach((estimate, index) => {
        const origin = { lat: estimate.fromCoordinates[0], lng: estimate.fromCoordinates[1] };
        const destination = { lat: estimate.toCoordinates[0], lng: estimate.toCoordinates[1] };

        const renderer = new google.maps.DirectionsRenderer({
            suppressMarkers: true // Suppress default markers
        });
        renderer.setMap(map.value);
        directionsRenderers.value.push(renderer);

        // Create destination marker for each estimate (subsequent stops)
        const destinationMarker = new google.maps.Marker({
            position: destination,
            map: map.value,
            label: {
                text: String(markerCounter++), // Increment for each destination
                color: '#FFFFFF',
                fontWeight: 'bold'
            },
            title: `Destination for Estimate ${index + 1}: ${estimate.to} (Travel Time: ${estimate.durationMinutes} min)`
        });
        customMarkers.value.push(destinationMarker);

        // Store the original estimate data on the marker for easy access in click listener
        (destinationMarker as any)._travelEstimate = estimate;

        // Add click listener to destination marker to show InfoWindow
        destinationMarker.addListener('click', () => {
            if (infoWindow.value && map.value) {
                const clickedEstimate = (destinationMarker as any)._travelEstimate;
                const actualDuration = (destinationMarker as any)._actualDurationText || `${clickedEstimate.durationMinutes} minutes (approx)`;
                const trafficDuration = (destinationMarker as any)._trafficDurationText;

                infoWindow.value.setContent(`
                    <div>
                        <strong>Destination:</strong> ${clickedEstimate.to}<br/>
                        <strong>Travel Time:</strong> ${actualDuration}
                        ${trafficDuration ? `<br/><strong>Traffic Time:</strong> ${trafficDuration}` : ''}
                    </div>
                `);
                infoWindow.value.open(map.value, destinationMarker);
            } else {
                console.error('InfoWindow or map is null, cannot open InfoWindow.');
            }
        });

        directionsService.value?.route(
            {
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING,
                drivingOptions: {
                    departureTime: new Date(), // Use current time for traffic estimate
                    trafficModel: google.maps.TrafficModel.BEST_GUESS
                }
            },
            (response, status) => {
                if (status === 'OK') {
                    renderer.setDirections(response);
                    const leg = response.routes[0].legs[0];
                    // Store the actual duration text (without traffic) on the marker
                    (destinationMarker as any)._actualDurationText = leg.duration.text;

                    // Store the traffic-aware duration text and value if available
                    if (leg.duration_in_traffic) {
                        (destinationMarker as any)._trafficDurationText = leg.duration_in_traffic.text;
                        routeLegsData.value.push({ duration: leg.duration, distance: leg.distance, duration_in_traffic: leg.duration_in_traffic });
                    } else {
                        (destinationMarker as any)._trafficDurationText = null; // No traffic data available
                        routeLegsData.value.push({ duration: leg.duration, distance: leg.distance });
                    }
                } else {
                    console.error('Directions request failed due to ' + status);
                    (destinationMarker as any)._actualDurationText = 'N/A'; // Fallback if directions fail
                    (destinationMarker as any)._trafficDurationText = null;
                }
            }
        );
    });
};

onMounted(() => {
    // Wait for Google Maps API to be loaded by another component (e.g., LocationPicker)
    const checkGoogleMaps = setInterval(() => {
        if (window.google && window.google.maps) {
            clearInterval(checkGoogleMaps);
            initMap();
        }
    }, 100);
});

watch(
    () => props.travelEstimate,
    () => {
        initMap();
    },
    { deep: true }
);
</script>

<style scoped lang="scss">
.travel-estimates-map {
    margin-top: 20px;
}

.map-container {
    margin-top: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* PrimeVue Card overrides for a cleaner look */
.p-card {
    border-radius: 12px;
    box-shadow: none;
    background-color: var(--surface-card);
    border: none;
    :deep(.p-card-body) {
        padding: 0;
    }
}

.p-card .p-card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.p-card .p-card-content {
    padding: 0;
}

/* PrimeVue Fieldset overrides */
.p-fieldset {
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    margin-bottom: 1rem; /* Space between fieldsets */
    background-color: var(--surface-ground); /* Slightly different background for distinction */
}

.p-fieldset .p-fieldset-legend {
    font-weight: 600;
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background-color: var(--surface-card);
    border: 1px solid var(--surface-border);
}

.p-fieldset .p-fieldset-content {
    padding: 1rem;
}

.p-field {
    margin-bottom: 0.75rem; /* Adjusted for better spacing within fieldset */
}

.p-field:last-child {
    margin-bottom: 0;
}

.text-primary {
    color: var(--primary-color);
}

.font-semibold {
    font-weight: 600;
}

.ml-2 {
    margin-left: 0.5rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.p-message {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.total-summary {
    padding: 1rem;
    background-color: var(--surface-ground);
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}
</style>
