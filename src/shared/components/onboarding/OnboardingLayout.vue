<template>
    <div class="onboarding-layout" :class="layoutClasses">
        <div class="layout-container">
            <!-- Header Section -->
            <div v-if="$slots.header" class="layout-header">
                <slot name="header"></slot>
            </div>

            <!-- Steps Section -->
            <div v-if="$slots.steps" class="layout-steps">
                <slot name="steps"></slot>
            </div>

            <!-- Main Content -->
            <div class="layout-content">
                <slot></slot>
            </div>

            <!-- Footer Section -->
            <div v-if="$slots.footer" class="layout-footer">
                <slot name="footer"></slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
interface Props {
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
    padding?: 'none' | 'sm' | 'md' | 'lg';
    background?: 'default' | 'surface' | 'transparent';
    centered?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    maxWidth: 'lg',
    padding: 'md',
    background: 'default',
    centered: true
});

// Computed
const layoutClasses = computed(() => {
    return {
        [`layout-${props.background}`]: true,
        [`layout-padding-${props.padding}`]: true,
        'layout-centered': props.centered
    };
});
</script>

<style scoped>
.onboarding-layout {
    @apply min-h-screen w-full;
}

/* Background variants */
.layout-default {
    @apply bg-white dark:bg-surface-900;
}

.layout-surface {
    @apply bg-surface-50 dark:bg-surface-950;
}

.layout-transparent {
    @apply bg-transparent;
}

/* Container */
.layout-container {
    @apply w-full mx-auto;
}

/* Max width variants */
.onboarding-layout .layout-container {
}

.onboarding-layout:has(.layout-container) {
    /* sm */
    &[class*='max-width-sm'] .layout-container {
        max-width: 640px;
    }

    /* md */
    &[class*='max-width-md'] .layout-container {
        max-width: 768px;
    }

    /* xl */
    &[class*='max-width-xl'] .layout-container {
        max-width: 1280px;
    }

    /* 2xl */
    &[class*='max-width-2xl'] .layout-container {
        max-width: 1536px;
    }

    /* full */
    &[class*='max-width-full'] .layout-container {
        max-width: 100%;
    }
}

/* Padding variants */
.layout-padding-none {
    @apply p-0;
}

.layout-padding-sm .layout-container {
    @apply px-4 py-6;
}

.layout-padding-md .layout-container {
    @apply px-6 py-8;
}

.layout-padding-lg .layout-container {
    @apply px-8 py-12;
}

/* Centered layout */
.layout-centered .layout-container {
    @apply mx-auto;
}

/* Layout sections */
.layout-header {
    @apply mb-8;
}

.layout-steps {
    @apply mb-8;
}

.layout-content {
    @apply flex-1;
}

.layout-footer {
    @apply mt-8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .layout-padding-sm .layout-container {
        @apply px-3 py-4;
    }

    .layout-padding-md .layout-container {
        @apply px-4 py-6;
    }

    .layout-padding-lg .layout-container {
        @apply px-6 py-8;
    }

    .layout-header {
        @apply mb-6;
    }

    .layout-steps {
        @apply mb-6;
    }

    .layout-footer {
        @apply mt-6;
    }
}
</style>
