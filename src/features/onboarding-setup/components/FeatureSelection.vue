<template>
    <div class="feature-selection">
        <OnboardingLayout background="surface" max-width="xl">
            <template #header>
                <OnboardingHeader
                    title="Choose Your Setup Features"
                    description="Select the features you'd like to set up now. You can always configure additional features later from your dashboard."
                    icon="pi pi-list-check"
                    :show-actions="true"
                    :show-back-button="true"
                    @back="$emit('back')"
                />
            </template>

            <div class="selection-content">
                <!-- Selection Summary -->
                <Card class="selection-summary mb-6">
                    <template #content>
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <div>
                                <h3 class="text-lg font-semibold mb-2">Selected Features</h3>
                                <p class="text-surface-600 dark:text-surface-400">{{ selectedFeatures.length }} of {{ totalFeatures }} features selected • Estimated time: {{ estimatedTime }}</p>
                            </div>
                            <div class="flex gap-2">
                                <Button label="Select All" icon="pi pi-check-square" severity="secondary" outlined size="small" @click="selectAll" />
                                <Button label="Clear All" icon="pi pi-square" severity="secondary" outlined size="small" @click="clearAll" />
                            </div>
                        </div>
                    </template>
                </Card>

                <!-- Feature Categories -->
                <div class="space-y-8">
                    <div v-for="category in categories" :key="category.id" class="category-section">
                        <div class="category-header mb-4">
                            <div class="flex items-center gap-3 mb-2">
                                <i :class="[category.icon, category.color]" class="text-2xl"></i>
                                <h2 class="text-xl font-bold text-surface-900 dark:text-surface-0">
                                    {{ category.title }}
                                </h2>
                            </div>
                            <p class="text-surface-600 dark:text-surface-400">
                                {{ category.description }}
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div v-for="feature in getCategoryFeatures(category.id)" :key="feature.id" class="feature-card" :class="{ selected: isFeatureSelected(feature.id), required: feature.isRequired }" @click="toggleFeature(feature.id)">
                                <Card class="h-full cursor-pointer transition-all duration-200 hover:shadow-lg">
                                    <template #content>
                                        <div class="feature-content">
                                            <!-- Feature Header -->
                                            <div class="flex items-start justify-between mb-3">
                                                <div class="flex items-center gap-3">
                                                    <i :class="[feature.icon, feature.color || 'text-primary']" class="text-xl"></i>
                                                    <div class="flex-1">
                                                        <h3 class="font-semibold text-surface-900 dark:text-surface-0">
                                                            {{ feature.title }}
                                                        </h3>
                                                        <div class="flex items-center gap-2 mt-1">
                                                            <Tag v-if="feature.isRequired" value="Required" severity="warning" class="text-xs" />
                                                            <span class="text-xs text-surface-500 dark:text-surface-400">
                                                                {{ feature.estimatedTime }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <Checkbox :model-value="isFeatureSelected(feature.id)" :disabled="feature.isRequired" @update:model-value="toggleFeature(feature.id)" @click.stop />
                                            </div>

                                            <!-- Feature Description -->
                                            <p class="text-sm text-surface-600 dark:text-surface-400 mb-4">
                                                {{ feature.description }}
                                            </p>

                                            <!-- Feature Benefits -->
                                            <div v-if="getFeatureBenefits(feature.id).length > 0" class="feature-benefits">
                                                <div class="text-xs font-medium text-surface-700 dark:text-surface-300 mb-2">Key Benefits:</div>
                                                <ul class="space-y-1">
                                                    <li v-for="benefit in getFeatureBenefits(feature.id)" :key="benefit" class="text-xs text-surface-600 dark:text-surface-400 flex items-center gap-2">
                                                        <i class="pi pi-check text-green-500 text-xs"></i>
                                                        {{ benefit }}
                                                    </li>
                                                </ul>
                                            </div>

                                            <!-- Dependencies -->
                                            <div v-if="getFeatureDependencies(feature.id).length > 0" class="feature-dependencies mt-3">
                                                <div class="text-xs font-medium text-surface-700 dark:text-surface-300 mb-1">Requires:</div>
                                                <div class="flex flex-wrap gap-1">
                                                    <Tag v-for="dep in getFeatureDependencies(feature.id)" :key="dep" :value="dep" severity="info" class="text-xs" />
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="selection-actions mt-8">
                    <Card>
                        <template #content>
                            <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                                <div class="text-center sm:text-left">
                                    <div class="font-semibold text-surface-900 dark:text-surface-0">Ready to start setup?</div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">{{ selectedFeatures.length }} features selected • {{ estimatedTime }} estimated</div>
                                </div>
                                <div class="flex gap-3">
                                    <Button label="Back" icon="pi pi-arrow-left" severity="secondary" outlined @click="$emit('back')" />
                                    <Button label="Start Setup" icon="pi pi-play" :disabled="selectedFeatures.length === 0" @click="startSetup" />
                                </div>
                            </div>
                        </template>
                    </Card>
                </div>
            </div>
        </OnboardingLayout>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// PrimeVue Components
import Card from 'primevue/card';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import Tag from 'primevue/tag';

// Shared Components
import { OnboardingLayout, OnboardingHeader } from '@/shared/components/onboarding';

// Types
import type { FeatureConfig, FeatureCategory } from '../types';
import { FEATURE_DEPENDENCIES } from '../constants';

// Props
interface Props {
    features: Record<string, FeatureConfig>;
    categories: FeatureCategory[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    'features-selected': [features: string[]];
    back: [];
}>();

// State
const selectedFeatures = ref<string[]>([]);

// Initialize with required features
const initializeSelection = () => {
    const requiredFeatures = Object.entries(props.features)
        .filter(([_, feature]) => feature.isRequired)
        .map(([id, _]) => id);
    selectedFeatures.value = [...requiredFeatures];
};

// Initialize on mount
initializeSelection();

// Computed
const totalFeatures = computed(() => Object.keys(props.features).length);

const estimatedTime = computed(() => {
    const totalMinutes = selectedFeatures.value.reduce((total, featureId) => {
        const feature = props.features[featureId];
        if (feature) {
            const minutes = parseInt(feature.estimatedTime.match(/\d+/)?.[0] || '0');
            return total + minutes;
        }
        return total;
    }, 0);

    if (totalMinutes === 0) return '0 minutes';
    if (totalMinutes < 60) return `${totalMinutes} minutes`;

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
});

// Methods
const getCategoryFeatures = (categoryId: string) => {
    return Object.entries(props.features)
        .filter(([_, feature]) => feature.category === categoryId)
        .map(([id, feature]) => ({ id, ...feature }));
};

const isFeatureSelected = (featureId: string): boolean => {
    return selectedFeatures.value.includes(featureId);
};

const toggleFeature = (featureId: string) => {
    const feature = props.features[featureId];
    if (feature?.isRequired) return; // Can't toggle required features

    const index = selectedFeatures.value.indexOf(featureId);
    if (index > -1) {
        selectedFeatures.value.splice(index, 1);
        // Also remove dependent features
        removeDependentFeatures(featureId);
    } else {
        selectedFeatures.value.push(featureId);
        // Also add dependency features
        addDependencyFeatures(featureId);
    }
};

const addDependencyFeatures = (featureId: string) => {
    const dependencies = FEATURE_DEPENDENCIES[featureId] || [];
    dependencies.forEach((depId) => {
        if (!selectedFeatures.value.includes(depId)) {
            selectedFeatures.value.push(depId);
        }
    });
};

const removeDependentFeatures = (featureId: string) => {
    // Find features that depend on this feature
    Object.entries(FEATURE_DEPENDENCIES).forEach(([dependentId, dependencies]) => {
        if (dependencies.includes(featureId)) {
            const index = selectedFeatures.value.indexOf(dependentId);
            if (index > -1) {
                selectedFeatures.value.splice(index, 1);
                // Recursively remove their dependents
                removeDependentFeatures(dependentId);
            }
        }
    });
};

const selectAll = () => {
    selectedFeatures.value = Object.keys(props.features);
};

const clearAll = () => {
    // Keep only required features
    const requiredFeatures = Object.entries(props.features)
        .filter(([_, feature]) => feature.isRequired)
        .map(([id, _]) => id);
    selectedFeatures.value = [...requiredFeatures];
};

const getFeatureBenefits = (featureId: string): string[] => {
    const benefits: Record<string, string[]> = {
        companyInfo: ['Professional business profile', 'Better lead qualification', 'Improved credibility'],
        userInfo: ['Personalized experience', 'Account security', 'Team collaboration'],
        calendar: ['Automated scheduling', 'Reduced no-shows', 'Better time management'],
        numbers: ['Direct customer communication', 'SMS marketing', 'Call tracking'],
        email: ['Automated responses', 'Lead capture', 'Professional communication'],
        prompts: ['Consistent AI responses', 'Better lead qualification', 'Improved conversion'],
        scripts: ['Personalized communication', 'Automated follow-ups', 'Brand consistency'],
        leadConfig: ['Custom workflows', 'Better organization', 'Improved tracking']
    };
    return benefits[featureId] || [];
};

const getFeatureDependencies = (featureId: string): string[] => {
    const dependencies = FEATURE_DEPENDENCIES[featureId] || [];
    return dependencies.map((depId) => props.features[depId]?.title || depId);
};

const startSetup = () => {
    emit('features-selected', selectedFeatures.value);
};
</script>

<style scoped>
.feature-selection {
    min-height: 100vh;
}

.selection-content {
    margin: 0 auto;
}

/* Category Section */
.category-section {
    margin-bottom: 3rem;
}

.category-header {
    text-align: center;
    margin-bottom: 2rem;
}

/* Feature Cards */
.feature-card {
    transition: all 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
}

.feature-card.selected :deep(.p-card) {
    border: 2px solid var(--primary-color);
    background: var(--primary-50);
}

.feature-card.required :deep(.p-card) {
    border: 2px solid var(--orange-400);
    background: var(--orange-50);
}

.dark .feature-card.selected :deep(.p-card) {
    background: var(--primary-900);
    border-color: var(--primary-400);
}

.dark .feature-card.required :deep(.p-card) {
    background: var(--orange-900);
    border-color: var(--orange-400);
}

.feature-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.feature-benefits {
    margin-top: auto;
}

.feature-dependencies {
    border-top: 1px solid var(--surface-border);
    padding-top: 0.75rem;
}

/* Selection Summary */
.selection-summary :deep(.p-card-content) {
    padding: 1.5rem;
}

/* Selection Actions */
.selection-actions {
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.selection-actions :deep(.p-card) {
    box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .category-header {
        text-align: left;
        margin-bottom: 1.5rem;
    }

    .feature-card {
        margin-bottom: 1rem;
    }

    .selection-actions {
        position: static;
        margin-top: 2rem;
    }

    .selection-actions :deep(.p-card) {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
    }
}

/* Animation for feature selection */
.feature-card :deep(.p-card) {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card.selected :deep(.p-card) {
    animation: selectPulse 0.3s ease-out;
}

@keyframes selectPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}
</style>
