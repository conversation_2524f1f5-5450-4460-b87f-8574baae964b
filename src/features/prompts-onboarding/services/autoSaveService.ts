import { ref, watch, nextTick } from 'vue';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import type { Tenant } from '@/entities/tenant/model';
import type { ChatPromptConfig, AnalysisPromptConfig, FollowupPromptConfig } from '../types';

// Auto-save configuration
interface AutoSaveConfig {
    debounceMs: number;
    maxRetries: number;
    retryDelayMs: number;
}

const DEFAULT_CONFIG: AutoSaveConfig = {
    debounceMs: 1000, // 1 second debounce
    maxRetries: 3,
    retryDelayMs: 2000
};

// Auto-save status type
export type AutoSaveStatus = 'idle' | 'saving' | 'saved' | 'error';

// Auto-save service class
export class AutoSaveService {
    private tenantStore = useTenantStore();
    private config: AutoSaveConfig;
    private saveTimeouts = new Map<string, NodeJS.Timeout>();
    private retryCounters = new Map<string, number>();
    private statusCallbacks = new Map<string, (status: AutoSaveStatus) => void>();

    constructor(config: Partial<AutoSaveConfig> = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
    }

    /**
     * Register a status callback for a specific field
     */
    onStatusChange(fieldKey: string, callback: (status: AutoSaveStatus) => void) {
        this.statusCallbacks.set(fieldKey, callback);
    }

    /**
     * Remove status callback
     */
    offStatusChange(fieldKey: string) {
        this.statusCallbacks.delete(fieldKey);
    }

    /**
     * Update status and notify callback
     */
    private updateStatus(fieldKey: string, status: AutoSaveStatus) {
        const callback = this.statusCallbacks.get(fieldKey);
        if (callback) {
            callback(status);
        }
    }

    /**
     * Auto-save chatbot prompt configuration
     */
    autoSaveChatPrompt(
        tenantId: string,
        promptConfig: ChatPromptConfig,
        fieldKey: string = 'chat-prompt'
    ) {
        this.debouncedSave(fieldKey, async () => {
            const updateData: Partial<Tenant> = {
                chat: {
                    prompt: promptConfig
                }
            };
            await this.tenantStore.updateTenant(tenantId, updateData);
        });
    }

    /**
     * Auto-save analysis prompt configuration
     */
    autoSaveAnalysisPrompt(
        tenantId: string,
        promptConfig: AnalysisPromptConfig,
        fieldKey: string = 'analysis-prompt'
    ) {
        this.debouncedSave(fieldKey, async () => {
            const updateData: Partial<Tenant> = {
                analysis: {
                    prompt: promptConfig
                }
            };
            await this.tenantStore.updateTenant(tenantId, updateData);
        });
    }

    /**
     * Auto-save followup prompt configuration
     */
    autoSaveFollowupPrompt(
        tenantId: string,
        promptConfig: FollowupPromptConfig & { prompt?: string },
        fieldKey: string = 'followup-prompt'
    ) {
        this.debouncedSave(fieldKey, async () => {
            const updateData: Partial<Tenant> = {
                followup: promptConfig
            };
            await this.tenantStore.updateTenant(tenantId, updateData);
        });
    }

    /**
     * Debounced save with retry logic
     */
    private debouncedSave(fieldKey: string, saveFunction: () => Promise<void>) {
        // Clear existing timeout
        const existingTimeout = this.saveTimeouts.get(fieldKey);
        if (existingTimeout) {
            clearTimeout(existingTimeout);
        }

        // Set new timeout
        const timeout = setTimeout(async () => {
            await this.executeSave(fieldKey, saveFunction);
        }, this.config.debounceMs);

        this.saveTimeouts.set(fieldKey, timeout);
    }

    /**
     * Execute save with retry logic
     */
    private async executeSave(fieldKey: string, saveFunction: () => Promise<void>) {
        this.updateStatus(fieldKey, 'saving');

        try {
            await saveFunction();
            this.updateStatus(fieldKey, 'saved');
            this.retryCounters.delete(fieldKey);

            // Reset to idle after a short delay
            setTimeout(() => {
                this.updateStatus(fieldKey, 'idle');
            }, 2000);

        } catch (error) {
            console.error(`Auto-save failed for ${fieldKey}:`, error);
            
            const retryCount = this.retryCounters.get(fieldKey) || 0;
            
            if (retryCount < this.config.maxRetries) {
                this.retryCounters.set(fieldKey, retryCount + 1);
                
                // Retry after delay
                setTimeout(() => {
                    this.executeSave(fieldKey, saveFunction);
                }, this.config.retryDelayMs);
                
            } else {
                this.updateStatus(fieldKey, 'error');
                this.retryCounters.delete(fieldKey);
            }
        }
    }

    /**
     * Cancel all pending saves
     */
    cancelAll() {
        this.saveTimeouts.forEach(timeout => clearTimeout(timeout));
        this.saveTimeouts.clear();
        this.retryCounters.clear();
    }

    /**
     * Cancel save for specific field
     */
    cancel(fieldKey: string) {
        const timeout = this.saveTimeouts.get(fieldKey);
        if (timeout) {
            clearTimeout(timeout);
            this.saveTimeouts.delete(fieldKey);
        }
        this.retryCounters.delete(fieldKey);
        this.updateStatus(fieldKey, 'idle');
    }

    /**
     * Force immediate save (bypass debounce)
     */
    async forceSave(fieldKey: string, saveFunction: () => Promise<void>) {
        this.cancel(fieldKey);
        await this.executeSave(fieldKey, saveFunction);
    }
}

// Composable for using auto-save service
export function useAutoSave(config?: Partial<AutoSaveConfig>) {
    const autoSaveService = new AutoSaveService(config);
    
    return {
        autoSaveService,
        
        // Convenience methods
        autoSaveChatPrompt: (tenantId: string, config: ChatPromptConfig, fieldKey?: string) =>
            autoSaveService.autoSaveChatPrompt(tenantId, config, fieldKey),
            
        autoSaveAnalysisPrompt: (tenantId: string, config: AnalysisPromptConfig, fieldKey?: string) =>
            autoSaveService.autoSaveAnalysisPrompt(tenantId, config, fieldKey),
            
        autoSaveFollowupPrompt: (tenantId: string, config: FollowupPromptConfig & { prompt?: string }, fieldKey?: string) =>
            autoSaveService.autoSaveFollowupPrompt(tenantId, config, fieldKey),
            
        onStatusChange: (fieldKey: string, callback: (status: AutoSaveStatus) => void) =>
            autoSaveService.onStatusChange(fieldKey, callback),
            
        offStatusChange: (fieldKey: string) =>
            autoSaveService.offStatusChange(fieldKey),
            
        cancel: (fieldKey: string) =>
            autoSaveService.cancel(fieldKey),
            
        cancelAll: () =>
            autoSaveService.cancelAll(),
            
        forceSave: (fieldKey: string, saveFunction: () => Promise<void>) =>
            autoSaveService.forceSave(fieldKey, saveFunction)
    };
}

// Reactive auto-save composable for components
export function useReactiveAutoSave(
    tenantId: string,
    config?: Partial<AutoSaveConfig>
) {
    const { autoSaveService, ...methods } = useAutoSave(config);
    const saveStatus = ref<Record<string, AutoSaveStatus>>({});

    // Track status changes
    const trackStatus = (fieldKey: string) => {
        autoSaveService.onStatusChange(fieldKey, (status) => {
            saveStatus.value[fieldKey] = status;
        });
    };

    // Auto-save with reactive tracking
    const autoSaveWithTracking = {
        chatPrompt: (config: ChatPromptConfig, fieldKey: string = 'chat-prompt') => {
            trackStatus(fieldKey);
            return methods.autoSaveChatPrompt(tenantId, config, fieldKey);
        },
        
        analysisPrompt: (config: AnalysisPromptConfig, fieldKey: string = 'analysis-prompt') => {
            trackStatus(fieldKey);
            return methods.autoSaveAnalysisPrompt(tenantId, config, fieldKey);
        },
        
        followupPrompt: (config: FollowupPromptConfig & { prompt?: string }, fieldKey: string = 'followup-prompt') => {
            trackStatus(fieldKey);
            return methods.autoSaveFollowupPrompt(tenantId, config, fieldKey);
        }
    };

    return {
        saveStatus,
        autoSave: autoSaveWithTracking,
        ...methods
    };
}
