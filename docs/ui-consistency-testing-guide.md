# UI Consistency Testing Guide

## Overview

This guide provides comprehensive testing procedures to ensure consistent behavior, styling, and user experience across all onboarding features and different screen sizes.

## Testing Scope

### Features to Test
1. ✅ Calendar Onboarding (`/src/features/calendar-onboarding`)
2. ✅ Company Info Update (`/src/features/company-info-update`)
3. ✅ Company User Update (`/src/features/company-user-update`)
4. ✅ Email Onboarding (`/src/features/email-onboarding`)
5. ✅ Lead Configuration (`/src/features/lead-configuration-onboarding`)
6. ✅ Numbers Onboarding (`/src/features/numbers-onboarding`)
7. ✅ Prompts Onboarding (`/src/features/prompts-onboarding`)
8. ✅ Scripts Onboarding (`/src/features/scripts-onboarding`)

### Screen Sizes to Test
- **Mobile**: 375px, 414px, 390px (iPhone sizes)
- **Tablet**: 768px, 1024px (iPad sizes)
- **Desktop**: 1280px, 1440px, 1920px (common desktop sizes)

## Design System Consistency

### Shared Components Usage
All features should use the standardized shared components:

#### ✅ OnboardingLayout
- Consistent max-width and padding
- Proper responsive behavior
- Background variants working correctly

#### ✅ OnboardingHeader
- Uniform title and description styling
- Consistent icon sizing and positioning
- Proper action button placement

#### ✅ OnboardingSteps
- Consistent step indicator design
- Uniform progress visualization
- Proper step navigation behavior

#### ✅ OnboardingCard
- Consistent card styling and spacing
- Uniform header, content, and footer layouts
- Proper variant styling (default, outlined, success, warning, error, info)

#### ✅ FeatureIcon
- Consistent icon sizing across features
- Uniform color variants
- Proper shape variants (circle, rounded, square)

### Visual Consistency Checklist

#### Typography
- [ ] Consistent heading hierarchy (h1, h2, h3)
- [ ] Uniform font weights and sizes
- [ ] Consistent line heights and spacing
- [ ] Proper text color usage (surface-900, surface-600, surface-400)

#### Colors
- [ ] Consistent primary color usage
- [ ] Proper semantic color application (success, warning, error, info)
- [ ] Uniform dark mode color handling
- [ ] Consistent hover and focus states

#### Spacing
- [ ] Uniform margin and padding values
- [ ] Consistent gap spacing in flex/grid layouts
- [ ] Proper component spacing relationships
- [ ] Uniform border radius values

#### Shadows and Borders
- [ ] Consistent card shadow styling
- [ ] Uniform border colors and widths
- [ ] Proper elevation hierarchy
- [ ] Consistent focus outline styling

## Functional Consistency Testing

### Navigation Patterns
- [ ] Consistent step navigation behavior
- [ ] Uniform button labeling (Previous, Next, Complete, etc.)
- [ ] Proper disabled state handling
- [ ] Consistent loading state indicators

### Form Interactions
- [ ] Uniform input field styling
- [ ] Consistent validation error display
- [ ] Proper form submission feedback
- [ ] Uniform required field indicators

### State Management
- [ ] Consistent loading states
- [ ] Uniform error handling and display
- [ ] Proper success state feedback
- [ ] Consistent empty state handling

## Responsive Testing Procedures

### Mobile Testing (< 640px)
1. **Layout Verification**
   - [ ] Content fits without horizontal scrolling
   - [ ] Proper stacking of elements
   - [ ] Adequate touch target sizes (min 44px)
   - [ ] Readable text without zooming

2. **Navigation Testing**
   - [ ] Step indicators work properly
   - [ ] Navigation buttons are accessible
   - [ ] Swipe gestures function correctly
   - [ ] Back/close buttons are properly positioned

3. **Form Testing**
   - [ ] Input fields are properly sized
   - [ ] No zoom on input focus (16px font minimum)
   - [ ] Proper keyboard behavior
   - [ ] Submit buttons are accessible

### Tablet Testing (640px - 1024px)
1. **Layout Adaptation**
   - [ ] Proper use of available space
   - [ ] Appropriate column layouts
   - [ ] Consistent spacing adjustments
   - [ ] Proper image and icon scaling

2. **Touch Interactions**
   - [ ] Touch targets remain accessible
   - [ ] Hover states work appropriately
   - [ ] Gesture support functions correctly
   - [ ] Orientation changes handled properly

### Desktop Testing (> 1024px)
1. **Full Feature Access**
   - [ ] All functionality is available
   - [ ] Proper hover state implementations
   - [ ] Keyboard navigation works correctly
   - [ ] Focus management is proper

2. **Layout Optimization**
   - [ ] Efficient use of screen real estate
   - [ ] Proper multi-column layouts
   - [ ] Appropriate maximum widths
   - [ ] Consistent spacing at larger sizes

## Cross-Browser Testing

### Required Browsers
- **Chrome** (latest 2 versions)
- **Firefox** (latest 2 versions)
- **Safari** (latest 2 versions)
- **Edge** (latest 2 versions)

### Mobile Browsers
- **iOS Safari** (latest 2 versions)
- **Chrome Mobile** (latest 2 versions)
- **Samsung Internet** (latest version)

### Testing Checklist per Browser
- [ ] Visual consistency maintained
- [ ] All interactions function properly
- [ ] Performance remains acceptable
- [ ] No console errors
- [ ] Proper fallbacks for unsupported features

## Accessibility Testing

### Keyboard Navigation
- [ ] All interactive elements are focusable
- [ ] Focus order is logical
- [ ] Focus indicators are visible
- [ ] Keyboard shortcuts work properly

### Screen Reader Testing
- [ ] Proper heading structure
- [ ] Descriptive link and button text
- [ ] Form labels are associated correctly
- [ ] Status updates are announced

### Visual Accessibility
- [ ] Sufficient color contrast ratios
- [ ] Information not conveyed by color alone
- [ ] Text scales properly up to 200%
- [ ] High contrast mode support

### Motor Accessibility
- [ ] Large enough touch targets
- [ ] Adequate spacing between elements
- [ ] Alternative input methods supported
- [ ] Reduced motion preferences respected

## Performance Testing

### Loading Performance
- [ ] Initial page load under 3 seconds
- [ ] Component rendering is smooth
- [ ] No layout shifts during loading
- [ ] Proper loading state indicators

### Runtime Performance
- [ ] Smooth animations and transitions
- [ ] Responsive user interactions
- [ ] No memory leaks
- [ ] Efficient re-rendering

### Network Considerations
- [ ] Graceful handling of slow connections
- [ ] Proper offline behavior
- [ ] Optimized asset loading
- [ ] Minimal data usage on mobile

## Testing Tools and Setup

### Development Tools
```bash
# Install testing dependencies
npm install --save-dev @testing-library/vue
npm install --save-dev @testing-library/jest-dom
npm install --save-dev cypress
```

### Browser Testing Setup
```javascript
// cypress.config.js
module.exports = {
  e2e: {
    viewportWidth: 1280,
    viewportHeight: 720,
    video: false,
    screenshotOnRunFailure: true
  }
}
```

### Responsive Testing Script
```javascript
// Test multiple viewports
const viewports = [
  { width: 375, height: 667 },   // iPhone SE
  { width: 414, height: 896 },   // iPhone 11
  { width: 768, height: 1024 },  // iPad
  { width: 1280, height: 720 },  // Desktop
  { width: 1920, height: 1080 }  // Large Desktop
];

viewports.forEach(viewport => {
  cy.viewport(viewport.width, viewport.height);
  // Run tests for each viewport
});
```

## Automated Testing

### Visual Regression Testing
```javascript
// Example visual test
describe('Onboarding Visual Consistency', () => {
  it('should maintain consistent styling across features', () => {
    cy.visit('/calendar-onboarding');
    cy.matchImageSnapshot('calendar-onboarding');
    
    cy.visit('/email-onboarding');
    cy.matchImageSnapshot('email-onboarding');
    
    // Test other features...
  });
});
```

### Component Testing
```javascript
// Example component test
import { mount } from '@vue/test-utils';
import OnboardingHeader from '@/shared/components/onboarding/OnboardingHeader.vue';

describe('OnboardingHeader', () => {
  it('should render consistently across features', () => {
    const wrapper = mount(OnboardingHeader, {
      props: {
        title: 'Test Title',
        description: 'Test Description',
        icon: 'pi pi-test'
      }
    });
    
    expect(wrapper.find('h1').text()).toBe('Test Title');
    expect(wrapper.find('p').text()).toBe('Test Description');
    expect(wrapper.find('i').classes()).toContain('pi-test');
  });
});
```

## Manual Testing Checklist

### Pre-Testing Setup
- [ ] Clear browser cache
- [ ] Disable browser extensions
- [ ] Set up testing environment
- [ ] Prepare test data

### Feature-by-Feature Testing
For each onboarding feature:

1. **Initial Load**
   - [ ] Page loads without errors
   - [ ] All components render correctly
   - [ ] Loading states display properly
   - [ ] No console errors

2. **Step Navigation**
   - [ ] Can navigate between steps
   - [ ] Step indicators update correctly
   - [ ] Progress is maintained
   - [ ] Validation works properly

3. **Form Interactions**
   - [ ] All inputs function correctly
   - [ ] Validation messages display
   - [ ] Submit actions work
   - [ ] Error handling is proper

4. **Responsive Behavior**
   - [ ] Layout adapts to screen size
   - [ ] Touch interactions work
   - [ ] Text remains readable
   - [ ] Navigation stays accessible

### Cross-Feature Consistency
- [ ] Visual styling is uniform
- [ ] Interaction patterns are consistent
- [ ] Navigation behavior is similar
- [ ] Error handling is uniform

## Issue Tracking and Resolution

### Issue Categories
1. **Critical**: Breaks functionality
2. **High**: Significant UX impact
3. **Medium**: Minor inconsistencies
4. **Low**: Cosmetic issues

### Issue Template
```markdown
## Issue Description
Brief description of the inconsistency

## Feature(s) Affected
- [ ] Calendar Onboarding
- [ ] Email Onboarding
- [ ] etc.

## Screen Size(s)
- [ ] Mobile (< 640px)
- [ ] Tablet (640px - 1024px)
- [ ] Desktop (> 1024px)

## Browser(s)
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## Steps to Reproduce
1. Step one
2. Step two
3. etc.

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Screenshots
Include relevant screenshots

## Priority
- [ ] Critical
- [ ] High
- [ ] Medium
- [ ] Low
```

## Sign-off Criteria

### Design Consistency ✅
- [ ] All features use shared components
- [ ] Visual styling is uniform
- [ ] Color usage is consistent
- [ ] Typography follows hierarchy

### Responsive Design ✅
- [ ] Mobile layouts work properly
- [ ] Tablet layouts are optimized
- [ ] Desktop layouts utilize space well
- [ ] Touch interactions are accessible

### Accessibility ✅
- [ ] Keyboard navigation works
- [ ] Screen readers function properly
- [ ] Color contrast is sufficient
- [ ] Motion preferences are respected

### Performance ✅
- [ ] Loading times are acceptable
- [ ] Interactions are responsive
- [ ] No memory leaks detected
- [ ] Network usage is optimized

### Cross-Browser Compatibility ✅
- [ ] All target browsers supported
- [ ] Fallbacks work properly
- [ ] No critical errors
- [ ] Consistent behavior maintained

## Maintenance Schedule

### Weekly
- [ ] Run automated visual regression tests
- [ ] Check for new browser updates
- [ ] Monitor performance metrics

### Monthly
- [ ] Full manual testing cycle
- [ ] Update testing documentation
- [ ] Review and update test cases

### Quarterly
- [ ] Comprehensive accessibility audit
- [ ] Performance optimization review
- [ ] Browser support matrix update
