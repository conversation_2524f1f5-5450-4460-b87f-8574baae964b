import { markRaw } from 'vue';
import type { Component } from 'vue';
import { COMPONENT_MAP } from '../constants';

// Import all feature components
import CompanyInfoUpdate from '@/features/company-info-update/CompanyInfoUpdate.vue';
import CompanyUserUpdate from '@/features/company-user-update/CompanyUserUpdate.vue';
import OnboardingCalendar from '@/features/calendar-onboarding/OnboardingCalendar.vue';
import OnboardingNumber from '@/features/numbers-onboarding/OnboardingNumber.vue';
import OnboardingEmail from '@/features/email-onboarding/OnboardingEmail.vue';
import OnboardingPrompts from '@/features/prompts-onboarding/OnboardingPrompts.vue';
import OnboardingScripts from '@/features/scripts-onboarding/OnboardingScripts.vue';
import OnboardingLeadConfiguration from '@/features/lead-configuration-onboarding/OnboardingLeadConfiguration.vue';

export class FeatureIntegrationService {
    private componentMap: Record<string, Component> = {};
    private componentRegistry: Record<string, Component> = {};

    constructor() {
        this.initializeComponentRegistry();
        this.initializeComponents();
    }

    // Initialize the component registry with all available components
    private initializeComponentRegistry() {
        this.componentRegistry = {
            CompanyInfoUpdate: markRaw(CompanyInfoUpdate),
            CompanyUserUpdate: markRaw(CompanyUserUpdate),
            OnboardingLeadConfiguration: markRaw(OnboardingLeadConfiguration),
            OnboardingCalendar: markRaw(OnboardingCalendar),
            OnboardingNumber: markRaw(OnboardingNumber),
            OnboardingEmail: markRaw(OnboardingEmail),
            OnboardingPrompts: markRaw(OnboardingPrompts),
            OnboardingScripts: markRaw(OnboardingScripts)
        };
    }

    // Initialize component mappings using COMPONENT_MAP from constants
    private initializeComponents() {
        this.componentMap = {};

        // Map feature IDs to components using COMPONENT_MAP
        Object.entries(COMPONENT_MAP).forEach(([featureId, componentName]) => {
            const component = this.componentRegistry[componentName];
            if (component) {
                this.componentMap[featureId] = component;
            } else {
                console.warn(`Component ${componentName} not found for feature ${featureId}`);
            }
        });
    }

    // Get component for feature
    getFeatureComponent(featureId: string): Component | null {
        return this.componentMap[featureId] || null;
    }

    // Get all available components
    getAllComponents(): Record<string, Component> {
        return { ...this.componentMap };
    }

    // Check if feature component exists
    hasFeatureComponent(featureId: string): boolean {
        return featureId in this.componentMap;
    }

    // Register a new component dynamically
    registerComponent(featureId: string, component: Component) {
        this.componentMap[featureId] = markRaw(component);
    }

    // Register multiple components at once
    registerComponents(components: Record<string, Component>) {
        Object.entries(components).forEach(([featureId, component]) => {
            this.registerComponent(featureId, component);
        });
    }

    // Get component mapping from constants
    getComponentMapping(): Record<string, string> {
        return { ...COMPONENT_MAP };
    }

    // Get feature component props configuration
    getFeatureProps(featureId: string): Record<string, any> {
        const defaultProps: Record<string, Record<string, any>> = {
            companyInfo: {
                showHeader: false,
                embedded: true
            },
            userInfo: {
                showHeader: false,
                embedded: true
            },
            leadConfig: {
                showHeader: false,
                embedded: true
            },
            calendar: {
                autoComplete: false
            },
            numbers: {
                autoComplete: false
            },
            email: {
                autoComplete: false
            },
            prompts: {
                autoSaveEnabled: true
            },
            scripts: {
                autoSaveEnabled: true
            }
        };

        return defaultProps[featureId] || {};
    }

    // Get feature event handlers
    getFeatureEventHandlers(featureId: string, onComplete: (data: any) => void, onError: (error: string) => void) {
        const handlers: Record<string, Record<string, Function>> = {
            companyInfo: {
                'update:completed': onComplete,
                error: onError
            },
            userInfo: {
                'update:completed': onComplete,
                error: onError
            },
            leadConfig: {
                complete: onComplete,
                error: onError
            },
            calendar: {
                complete: onComplete,
                error: onError
            },
            numbers: {
                complete: onComplete,
                error: onError
            },
            email: {
                complete: onComplete,
                error: onError
            },
            prompts: {
                complete: onComplete,
                error: onError
            },
            scripts: {
                complete: onComplete,
                error: onError
            }
        };

        return (
            handlers[featureId] || {
                complete: onComplete,
                error: onError
            }
        );
    }

    // Validate feature data
    validateFeatureData(featureId: string, data: any): { isValid: boolean; errors: string[] } {
        const validators: Record<string, (data: any) => { isValid: boolean; errors: string[] }> = {
            companyInfo: (data) => {
                const errors: string[] = [];
                if (!data?.name) errors.push('Company name is required');
                if (!data?.emailAddress) errors.push('Email address is required');
                return { isValid: errors.length === 0, errors };
            },
            userInfo: (data) => {
                const errors: string[] = [];
                if (!data?.firstName) errors.push('First name is required');
                if (!data?.lastName) errors.push('Last name is required');
                return { isValid: errors.length === 0, errors };
            },
            calendar: (data) => {
                const errors: string[] = [];
                if (!data?.calendarId) errors.push('Calendar setup is required');
                return { isValid: errors.length === 0, errors };
            }
        };

        const validator = validators[featureId];
        if (validator) {
            return validator(data);
        }

        // Default validation - just check if data exists
        return {
            isValid: data !== null && data !== undefined,
            errors: data ? [] : ['Feature data is required']
        };
    }

    // Transform feature data for storage
    transformFeatureData(featureId: string, data: any): any {
        const transformers: Record<string, (data: any) => any> = {
            companyInfo: (data) => ({
                companyName: data.name,
                email: data.emailAddress,
                phone: data.phone,
                website: data.website,
                industry: data.industry,
                description: data.description,
                address: {
                    city: data.city,
                    country: data.country
                }
            }),
            userInfo: (data) => ({
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                phone: data.phone,
                role: data.role
            }),
            leadConfig: (data) => ({
                leadActions: data.leadActions,
                leadSources: data.leadSources,
                leadStatuses: data.leadStatuses
            }),
            calendar: (data) => ({
                calendarId: data.calendarId,
                calendarName: data.calendarName,
                timezone: data.timezone
            }),
            numbers: (data) => ({
                serviceConfig: data.serviceConfig,
                purchaseResults: data.purchaseResults,
                validationStatus: data.validationStatus
            }),
            email: (data) => ({
                emailConfig: data.emailConfig,
                forwardingSetup: data.forwardingSetup
            }),
            prompts: (data) => ({
                chatbotPrompt: data.chatbotPrompt,
                analysisPrompt: data.analysisPrompt,
                followupPrompt: data.followupPrompt
            }),
            scripts: (data) => ({
                scripts: data.scripts,
                leadActions: data.leadActions
            })
        };

        const transformer = transformers[featureId];
        return transformer ? transformer(data) : data;
    }

    // Get feature completion criteria
    getCompletionCriteria(featureId: string): string[] {
        const criteria: Record<string, string[]> = {
            companyInfo: ['Company name provided', 'Contact email configured', 'Business information completed'],
            userInfo: ['User profile created', 'Contact information provided', 'Role and permissions set'],
            leadConfig: ['Lead actions configured', 'Lead sources defined', 'Lead statuses customized'],
            calendar: ['Google Calendar connected', 'Business calendar created', 'Timezone configured'],
            numbers: ['Communication services selected', 'Phone numbers configured', 'Integration validated'],
            email: ['Email forwarding configured', 'Chatbot email integration setup', 'Email routing validated'],
            prompts: ['AI chatbot prompts configured', 'Lead analysis prompts setup', 'Follow-up prompts created'],
            scripts: ['AI communication scripts created', 'Lead action scripts configured', 'Script templates setup']
        };

        return criteria[featureId] || ['Feature configuration completed'];
    }
}
