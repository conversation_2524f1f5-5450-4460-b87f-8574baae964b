<template>
    <Dialog :visible="visible" @update:visible="$emit('update:visible', $event)" modal header="Available Variables" :style="{ width: '50rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
        <div class="variables-help">
            <!-- Search -->
            <div class="mb-4">
                <IconField iconPosition="left">
                    <InputIcon class="pi pi-search" />
                    <InputText v-model="searchTerm" placeholder="Search variables..." class="w-full" />
                </IconField>
            </div>

            <!-- Category Tabs -->
            <TabView v-model:activeIndex="activeTab" class="mb-4">
                <TabPanel v-for="category in categories" :key="category.key" :header="category.label">
                    <template #header>
                        <div class="flex align-items-center gap-2">
                            <i :class="category.icon"></i>
                            <span>{{ category.label }}</span>
                            <Badge :value="category.count" severity="secondary" />
                        </div>
                    </template>

                    <!-- Variables Grid -->
                    <div class="variables-grid">
                        <div
                            v-for="variable in filteredVariablesByCategory(category.key)"
                            :key="variable.key"
                            class="variable-card p-3 border-1 border-200 border-round cursor-pointer hover:bg-blue-50 transition-colors"
                            @click="insertVariable(variable.key)"
                        >
                            <div class="flex justify-content-between align-items-start mb-2">
                                <div class="variable-name">
                                    <code class="text-primary font-semibold">[{{ variable.key }}]</code>
                                </div>
                                <Button icon="pi pi-plus" size="small" text severity="secondary" @click.stop="insertVariable(variable.key)" v-tooltip.left="'Insert variable'" />
                            </div>

                            <div class="variable-info">
                                <h4 class="text-900 font-medium mb-1">{{ variable.label }}</h4>
                                <p class="text-600 text-sm mb-2">{{ variable.description }}</p>
                                <div class="example">
                                    <span class="text-xs text-500 font-medium">Example:</span>
                                    <span class="text-xs text-700 ml-1">{{ variable.example }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No variables message -->
                    <div v-if="filteredVariablesByCategory(category.key).length === 0" class="text-center py-4 text-500">
                        <i class="pi pi-search text-2xl mb-2"></i>
                        <p>No variables found for "{{ searchTerm }}"</p>
                    </div>
                </TabPanel>
            </TabView>

            <!-- Action-specific recommendations -->
            <div v-if="actionRecommendations.length > 0" class="recommendations mt-4">
                <h3 class="text-lg font-semibold mb-3 flex align-items-center gap-2">
                    <i class="pi pi-lightbulb text-yellow-500"></i>
                    Recommended for {{ actionConfig?.label }}
                </h3>
                <div class="flex flex-wrap gap-2">
                    <Button v-for="variable in actionRecommendations" :key="variable.key" :label="`[${variable.key}]`" size="small" outlined @click="insertVariable(variable.key)">
                        <template #icon>
                            <i class="pi pi-plus mr-1"></i>
                        </template>
                    </Button>
                </div>
            </div>

            <!-- Usage Tips -->
            <div class="usage-tips mt-4 p-3 bg-blue-50 border-round">
                <h4 class="text-blue-900 font-semibold mb-2 flex align-items-center gap-2">
                    <i class="pi pi-info-circle"></i>
                    Usage Tips
                </h4>
                <ul class="text-blue-800 text-sm list-none p-0 m-0">
                    <li class="mb-1">• Variables are automatically replaced with actual values when the script is used</li>
                    <li class="mb-1">• Use square brackets around variable names: [customer_name]</li>
                    <li class="mb-1">• Variables are case-sensitive and must match exactly</li>
                    <li class="mb-1">• You can use the same variable multiple times in one script</li>
                </ul>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-content-end gap-2">
                <Button label="Close" severity="secondary" @click="$emit('update:visible', false)" />
            </div>
        </template>
    </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { DEFAULT_SCRIPT_VARIABLES } from '../types';
import type { ScriptVariable, ScriptVariablesHelpProps } from '../types';
import type { LeadConfigItem } from '@/features/lead-configuration/types';

// Props
interface Props {
    actionKey: string;
    visible: boolean;
    actionConfig?: LeadConfigItem;
}

const props = withDefaults(defineProps<Props>(), {
    visible: false
});

// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean];
    'insert-variable': [variableKey: string];
}>();

// State
const searchTerm = ref('');
const activeTab = ref(0);

// Available variables (could be extended with custom variables)
const availableVariables = ref<ScriptVariable[]>(DEFAULT_SCRIPT_VARIABLES);

// Categories configuration
const categories = computed(() => {
    const categoryMap = new Map<string, { key: string; label: string; icon: string; count: number }>();

    availableVariables.value.forEach((variable) => {
        if (!categoryMap.has(variable.category)) {
            categoryMap.set(variable.category, {
                key: variable.category,
                label: getCategoryLabel(variable.category),
                icon: getCategoryIcon(variable.category),
                count: 0
            });
        }
        categoryMap.get(variable.category)!.count++;
    });

    return Array.from(categoryMap.values()).sort((a, b) => a.label.localeCompare(b.label));
});

// Filtered variables
const filteredVariables = computed(() => {
    if (!searchTerm.value) return availableVariables.value;

    const term = searchTerm.value.toLowerCase();
    return availableVariables.value.filter((variable) => variable.key.toLowerCase().includes(term) || variable.label.toLowerCase().includes(term) || variable.description.toLowerCase().includes(term));
});

// Action-specific recommendations
const actionRecommendations = computed(() => {
    if (!props.actionKey) return [];

    const recommendations: Record<string, string[]> = {
        inquired: ['customer_name', 'agent_name', 'conversation_summary', 'service_type'],
        quoted: ['customer_name', 'agent_name', 'quote_amount', 'service_type'],
        booked: ['customer_name', 'agent_name', 'appointment_date', 'service_type'],
        converted: ['customer_name', 'agent_name', 'company_name', 'service_type']
    };

    const recommendedKeys = recommendations[props.actionKey] || [];
    return availableVariables.value.filter((variable) => recommendedKeys.includes(variable.key));
});

// Methods
const filteredVariablesByCategory = (category: string) => {
    return filteredVariables.value.filter((variable) => variable.category === category);
};

const insertVariable = (variableKey: string) => {
    emit('insert-variable', variableKey);
};

const getCategoryLabel = (category: string): string => {
    const labels: Record<string, string> = {
        customer: 'Customer',
        agent: 'Agent',
        business: 'Business',
        action: 'Action',
        system: 'System'
    };
    return labels[category] || category;
};

const getCategoryIcon = (category: string): string => {
    const icons: Record<string, string> = {
        customer: 'pi pi-user',
        agent: 'pi pi-users',
        business: 'pi pi-building',
        action: 'pi pi-bolt',
        system: 'pi pi-cog'
    };
    return icons[category] || 'pi pi-tag';
};

// Reset search when dialog opens/closes
watch(
    () => props.visible,
    (newVisible) => {
        if (newVisible) {
            searchTerm.value = '';
            activeTab.value = 0;
        }
    }
);
</script>

<style scoped lang="scss">
.variables-help {
    @apply min-h-96;
}

.variables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.variable-card {
    @apply transition-all duration-200;
}

.variable-card:hover {
    @apply shadow-lg border-primary-200;
}

.variable-name code {
    @apply text-sm px-2 py-1 bg-primary-50 rounded-lg;
}

.recommendations {
    @apply border-t border-surface-200 dark:border-surface-700 pt-4;
}

.usage-tips {
    @apply border-l-4 border-blue-400;
}

:deep(.p-tabview-nav) {
    @apply rounded-t-lg;
}

:deep(.p-tabview-panels) {
    @apply p-0;
}

:deep(.p-tabpanel) {
    @apply p-4;
}
</style>
