<template>
    <div class="lead-actions-script-manager">
        <!-- Header -->
        <div class="manager-header mb-4">
            <div class="flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-2xl font-semibold text-900 mb-2">Create AI Scripts</h2>
                    <p class="text-600">Create personalized scripts for each of your lead actions. These scripts will be used by AI to communicate with your leads.</p>
                </div>
                <div class="flex align-items-center gap-2">
                    <Button icon="pi pi-question-circle" severity="secondary" text @click="showUsageInfo = true" v-tooltip.left="'Show usage information'" />
                    <Button icon="pi pi-eye" severity="secondary" text @click="showExamples = true" v-tooltip.left="'View examples'" />
                </div>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="progress-overview mb-4">
            <Card>
                <template #content>
                    <div class="flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-lg font-semibold mb-1">Progress</h3>
                            <p class="text-600 text-sm">{{ completedScripts }} of {{ totalScripts }} scripts completed</p>
                        </div>
                        <div class="flex align-items-center gap-3">
                            <ProgressBar :value="progressPercentage" :show-value="false" class="w-20rem" />
                            <span class="text-lg font-semibold text-primary"> {{ progressPercentage }}% </span>
                        </div>
                    </div>
                </template>
            </Card>
        </div>

        <!-- Loading State -->

        <div v-if="isLoading" class="text-center py-8">
            <ProgressSpinner />
            <p class="text-600 mt-3">Loading your lead actions...</p>
        </div>

        <!-- No Lead Actions -->
        <div v-else-if="leadActionScripts.length === 0" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-400 mb-3"></i>
            <h3 class="text-xl font-semibold text-600 mb-2">No Lead Actions Found</h3>
            <p class="text-600 mb-4">You need to configure your lead actions first before creating scripts.</p>
            <Button label="Configure Lead Actions" icon="pi pi-cog" @click="$emit('configure-lead-actions')" />
        </div>

        <!-- Script Editors -->
        <div v-else class="script-editors">
            <div class="grid">
                <div v-for="actionScript in leadActionScripts" :key="actionScript.actionKey" class="col-12 xl:col-6">
                    <Card class="h-full">
                        <template #content>
                            <ScriptEditor
                                :model-value="actionScript.script.content"
                                :action-key="actionScript.actionKey"
                                :action-config="actionScript.actionConfig"
                                :auto-save="autoSaveConfig"
                                :disabled="disabled"
                                @update:model-value="updateScript(actionScript.actionKey, $event)"
                                @script-change="onScriptChange(actionScript.actionKey, $event)"
                                @validation-change="onValidationChange(actionScript.actionKey, $event)"
                                @save-status-change="onSaveStatusChange(actionScript.actionKey, $event)"
                            />
                        </template>
                    </Card>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div v-if="leadActionScripts.length > 0" class="action-buttons mt-4 flex justify-content-between">
            <div class="flex gap-2">
                <Button label="Load Templates" icon="pi pi-file-import" severity="secondary" @click="loadTemplates" :disabled="disabled" />
                <Button label="Clear All" icon="pi pi-trash" severity="danger" outlined @click="confirmClearAll" :disabled="disabled" />
            </div>
            <div class="flex gap-2">
                <Button label="Save All" icon="pi pi-save" @click="saveAllScripts" :disabled="disabled || !hasUnsavedChanges" :loading="isSaving" />
                <Button label="Continue" icon="pi pi-arrow-right" @click="$emit('continue')" :disabled="!allScriptsValid" />
            </div>
        </div>

        <!-- Usage Info Dialog -->
        <ScriptUsageInfo :visible="showUsageInfo" @update:visible="showUsageInfo = $event" />

        <!-- Examples Dialog -->
        <ScriptExamples :visible="showExamples" :lead-actions="enabledLeadActions" @update:visible="showExamples = $event" @use-template="useTemplate" />

        <!-- Clear Confirmation Dialog -->
        <ConfirmDialog />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useConfirm } from 'primevue/useconfirm';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import { useScriptAutoSave } from '../services/scriptAutoSaveService';
import ScriptEditor from './ScriptEditor.vue';
import ScriptUsageInfo from './ScriptUsageInfo.vue';
import ScriptExamples from './ScriptExamples.vue';
import type { LeadActionScript, ScriptConfig, ScriptValidationResult, ScriptAutoSaveStatus, ScriptAutoSaveConfig } from '../types';
import type { LeadConfigItem } from '@/features/lead-configuration/types';
import type { Tenant } from '@/entities/tenant/model';

// Props
interface Props {
    disabled?: boolean;
    autoSave?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    autoSave: true
});

// Emits
const emit = defineEmits<{
    'scripts-change': [scripts: Record<string, ScriptConfig>];
    'progress-change': [progress: number];
    'configure-lead-actions': [];
    continue: [];
}>();

// Composables
const authStore = useAuthStore();
const tenantStore = useTenantStore();
const confirm = useConfirm();
const toast = useToast();

// State
const isLoading = ref(true);
const isSaving = ref(false);
const showUsageInfo = ref(false);
const showExamples = ref(false);
const hasUnsavedChanges = ref(false);

const leadActionScripts = ref<LeadActionScript[]>([]);
const scriptValidations = ref<Record<string, ScriptValidationResult>>({});
const saveStatuses = ref<Record<string, ScriptAutoSaveStatus>>({});

// Get user data and tenant ID
const userData = authStore.getUserData();
const tenantId = userData?.tenantId;

// Auto-save configuration
const autoSaveConfig = computed<ScriptAutoSaveConfig | undefined>(() => {
    if (!props.autoSave || !tenantId) return undefined;

    return {
        enabled: true,
        debounceMs: 1000,
        tenantId,
        maxRetries: 3,
        retryDelayMs: 2000
    };
});

// Auto-save service
const { autoSaveScripts, forceSave } = useScriptAutoSave(tenantId || '');

// Computed properties
const enabledLeadActions = computed(() => {
    return leadActionScripts.value.filter((script) => script.actionConfig.status === 'enabled').map((script) => ({ key: script.actionKey, config: script.actionConfig }));
});

const totalScripts = computed(() => leadActionScripts.value.length);

const completedScripts = computed(() => {
    return leadActionScripts.value.filter((script) => script.script.content.trim().length > 0 && scriptValidations.value[script.actionKey]?.isValid !== false).length;
});

const progressPercentage = computed(() => {
    if (totalScripts.value === 0) return 0;
    return Math.round((completedScripts.value / totalScripts.value) * 100);
});

const allScriptsValid = computed(() => {
    return leadActionScripts.value.every((script) => {
        const validation = scriptValidations.value[script.actionKey];
        return script.script.content.trim().length > 0 && validation?.isValid !== false;
    });
});

// Methods
const loadTenantData = async () => {
    if (!tenantId) return;

    try {
        isLoading.value = true;
        const tenant = await tenantStore.getTenant(tenantId);
        console.log('========');
        console.log(tenant?.lead_actions);
        if (tenant?.lead_actions) {
            initializeScripts(tenant);
        }
    } catch (error) {
        console.error('Error loading tenant data:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load lead actions configuration',
            life: 3000
        });
    } finally {
        isLoading.value = false;
    }
};

const initializeScripts = (tenant: Tenant) => {
    const leadActions = tenant.lead_actions || {};
    const existingScripts = tenant.scripts || {};

    leadActionScripts.value = Object.entries(leadActions)
        .filter(([_, config]) => config.status === 'enabled')
        .map(([actionKey, actionConfig]) => ({
            actionKey,
            actionConfig,
            script: existingScripts[actionKey] || {
                content: '',
                variables: [],
                lastUpdated: new Date(),
                isValid: false
            },
            isRequired: true,
            isCompleted: false
        }));
};

const updateScript = (actionKey: string, content: string) => {
    const scriptIndex = leadActionScripts.value.findIndex((s) => s.actionKey === actionKey);
    if (scriptIndex >= 0) {
        leadActionScripts.value[scriptIndex].script.content = content;
        hasUnsavedChanges.value = true;
        emitScriptsChange();
    }
};

const onScriptChange = (actionKey: string, scriptConfig: ScriptConfig) => {
    const scriptIndex = leadActionScripts.value.findIndex((s) => s.actionKey === actionKey);
    if (scriptIndex >= 0) {
        leadActionScripts.value[scriptIndex].script = scriptConfig;
        emitScriptsChange();
    }
};

const onValidationChange = (actionKey: string, validation: ScriptValidationResult) => {
    scriptValidations.value[actionKey] = validation;
};

const onSaveStatusChange = (actionKey: string, status: ScriptAutoSaveStatus) => {
    saveStatuses.value[actionKey] = status;

    if (status === 'saved') {
        hasUnsavedChanges.value = false;
    }
};

const emitScriptsChange = () => {
    const scripts: Record<string, ScriptConfig> = {};
    leadActionScripts.value.forEach((actionScript) => {
        scripts[actionScript.actionKey] = actionScript.script;
    });
    emit('scripts-change', scripts);
    emit('progress-change', progressPercentage.value);
};

const loadTemplates = () => {
    // Implementation would load default templates
    toast.add({
        severity: 'info',
        summary: 'Templates Loaded',
        detail: 'Default script templates have been loaded',
        life: 3000
    });
};

const useTemplate = (actionKey: string, template: string) => {
    updateScript(actionKey, template);
    showExamples.value = false;
    toast.add({
        severity: 'success',
        summary: 'Template Applied',
        detail: `Template applied to ${actionKey} script`,
        life: 3000
    });
};

const confirmClearAll = () => {
    confirm.require({
        message: 'Are you sure you want to clear all scripts? This action cannot be undone.',
        header: 'Clear All Scripts',
        icon: 'pi pi-exclamation-triangle',
        rejectClass: 'p-button-secondary p-button-outlined',
        rejectLabel: 'Cancel',
        acceptLabel: 'Clear All',
        accept: clearAllScripts
    });
};

const clearAllScripts = () => {
    leadActionScripts.value.forEach((actionScript) => {
        actionScript.script.content = '';
        actionScript.script.variables = [];
        actionScript.script.lastUpdated = new Date();
    });
    hasUnsavedChanges.value = true;
    emitScriptsChange();

    toast.add({
        severity: 'info',
        summary: 'Scripts Cleared',
        detail: 'All scripts have been cleared',
        life: 3000
    });
};

const saveAllScripts = async () => {
    if (!tenantId) return;

    try {
        isSaving.value = true;

        const scripts: Record<string, ScriptConfig> = {};
        leadActionScripts.value.forEach((actionScript) => {
            scripts[actionScript.actionKey] = {
                ...actionScript.script,
                lastUpdated: new Date()
            };
        });

        await autoSaveScripts(scripts);
        hasUnsavedChanges.value = false;

        toast.add({
            severity: 'success',
            summary: 'Scripts Saved',
            detail: 'All scripts have been saved successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving scripts:', error);
        toast.add({
            severity: 'error',
            summary: 'Save Failed',
            detail: 'Failed to save scripts. Please try again.',
            life: 3000
        });
    } finally {
        isSaving.value = false;
    }
};

// Lifecycle
onMounted(() => {
    loadTenantData();
});

// Watch for progress changes
watch(progressPercentage, (newProgress) => {
    emit('progress-change', newProgress);
});
</script>

<style scoped lang="scss">
.lead-actions-script-manager {
    @apply w-full;
}

.manager-header {
    @apply border-b border-surface-200 dark:border-surface-700 pb-4;
}

.progress-overview :deep(.p-card-body) {
    @apply p-3;
}

.script-editors .p-card {
    @apply h-full;
}

.action-buttons {
    @apply border-t border-surface-200 dark:border-surface-700 pt-4;
}

:deep(.p-progressbar) {
    @apply h-2;
}

:deep(.p-progressbar .p-progressbar-value) {
    @apply transition-all duration-300;
}
</style>
