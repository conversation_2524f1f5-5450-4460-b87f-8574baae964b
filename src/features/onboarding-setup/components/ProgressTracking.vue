<template>
    <div class="progress-tracking" :class="{ 'compact-mode': !showFullView }">
        <!-- Compact View -->
        <div v-if="!showFullView" class="compact-progress">
            <div class="compact-header">
                <div class="flex items-center justify-between">
                    <div class="progress-info">
                        <div class="flex items-center gap-3">
                            <div class="progress-circle">
                                <div class="progress-text">{{ progress.progressPercentage }}%</div>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-surface-900 dark:text-surface-0">
                                    Setup Progress
                                </div>
                                <div class="text-xs text-surface-600 dark:text-surface-400">
                                    {{ progress.completedFeatures }}/{{ progress.totalFeatures }} completed • {{ progress.estimatedTimeRemaining }} left
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="compact-actions">
                        <Button
                            icon="pi pi-eye"
                            severity="secondary"
                            text
                            size="small"
                            @click="toggleFullView"
                            v-tooltip="'View detailed progress'"
                        />
                    </div>
                </div>
                <ProgressBar
                    :value="progress.progressPercentage"
                    class="compact-progress-bar mt-3"
                    :show-value="false"
                />
            </div>
        </div>

        <!-- Full View Modal -->
        <Dialog
            v-model:visible="showFullView"
            modal
            header="Setup Progress Details"
            :style="{ width: '90vw', maxWidth: '800px' }"
            :breakpoints="{ '960px': '75vw', '641px': '95vw' }"
            :draggable="false"
            :closable="true"
            @hide="showFullView = false"
        >
            <template #header>
                <div class="flex items-center gap-3">
                    <i class="pi pi-chart-line text-primary text-xl"></i>
                    <span class="font-semibold">Setup Progress Details</span>
                </div>
            </template>

            <!-- Full Progress Content -->
            <div class="full-progress-content">
                <!-- Overall Progress Section -->
                <div class="overall-progress mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <div>
                            <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                                Overall Progress
                            </h3>
                            <p class="text-sm text-surface-600 dark:text-surface-400">
                                {{ progress.completedFeatures }} of {{ progress.totalFeatures }} features completed
                            </p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-primary">
                                {{ progress.progressPercentage }}%
                            </div>
                            <div class="text-xs text-surface-500 dark:text-surface-400">
                                {{ progress.estimatedTimeRemaining }} remaining
                            </div>
                        </div>
                    </div>
                    <ProgressBar
                        :value="progress.progressPercentage"
                        class="progress-bar-custom"
                        :show-value="false"
                    />
                </div>

                <!-- Feature Steps -->
                <div class="feature-steps mb-6">
                    <h4 class="text-md font-semibold mb-4 text-surface-900 dark:text-surface-0">
                        Feature Setup Steps
                    </h4>
                    <div class="steps-container">
                        <div
                            v-for="(feature, index) in features"
                            :key="feature.id"
                            class="step-item"
                            :class="getStepClass(feature, index)"
                        >
                            <!-- Step Connector Line (before) -->
                            <div
                                v-if="index > 0"
                                class="step-connector-before"
                                :class="{ 'completed': features[index - 1].isCompleted || features[index - 1].isSkipped }"
                            ></div>

                            <!-- Step Circle -->
                            <div class="step-circle" @click="handleStepClick(feature)">
                                <div class="step-icon">
                                    <!-- Completed -->
                                    <i v-if="feature.isCompleted" class="pi pi-check text-white"></i>
                                    <!-- Skipped -->
                                    <i v-else-if="feature.isSkipped" class="pi pi-arrow-right text-white"></i>
                                    <!-- Current/Loading -->
                                    <i v-else-if="isCurrentFeature(feature)" class="pi pi-spin pi-spinner text-white"></i>
                                    <!-- Pending -->
                                    <i v-else :class="feature.icon" class="text-surface-400"></i>
                                </div>
                            </div>

                            <!-- Step Content -->
                            <div class="step-content">
                                <div class="step-header">
                                    <h4 class="step-title">{{ feature.title }}</h4>
                                    <div class="step-meta">
                                        <span class="step-time">{{ feature.estimatedTime }}</span>
                                        <Tag
                                            v-if="feature.isRequired"
                                            value="Required"
                                            severity="warning"
                                            class="step-tag"
                                        />
                                    </div>
                                </div>

                                <p class="step-description">{{ feature.description }}</p>

                                <!-- Step Status -->
                                <div class="step-status">
                                    <div v-if="feature.isCompleted" class="status-completed">
                                        <i class="pi pi-check-circle text-green-500"></i>
                                        <span class="text-green-600 dark:text-green-400">Completed</span>
                                    </div>
                                    <div v-else-if="feature.isSkipped" class="status-skipped">
                                        <i class="pi pi-arrow-circle-right text-orange-500"></i>
                                        <span class="text-orange-600 dark:text-orange-400">Skipped</span>
                                    </div>
                                    <div v-else-if="isCurrentFeature(feature)" class="status-current">
                                        <i class="pi pi-play-circle text-primary"></i>
                                        <span class="text-primary">In Progress</span>
                                    </div>
                                    <div v-else class="status-pending">
                                        <i class="pi pi-clock text-surface-400"></i>
                                        <span class="text-surface-500 dark:text-surface-400">Pending</span>
                                    </div>
                                </div>

                                <!-- Dependencies -->
                                <div v-if="getFeatureDependencies(feature.id).length > 0" class="step-dependencies">
                                    <div class="text-xs text-surface-600 dark:text-surface-400 mb-1">
                                        Requires:
                                    </div>
                                    <div class="flex flex-wrap gap-1">
                                        <Tag
                                            v-for="dep in getFeatureDependencies(feature.id)"
                                            :key="dep"
                                            :value="dep"
                                            severity="info"
                                            class="text-xs"
                                        />
                                    </div>
                                </div>
                            </div>

                            <!-- Step Connector Line (after) -->
                            <div
                                v-if="index < features.length - 1"
                                class="step-connector-after"
                                :class="{ 'completed': feature.isCompleted || feature.isSkipped }"
                            ></div>
                        </div>
                    </div>
                </div>

                <!-- Progress Summary -->
                <div class="progress-summary">
                    <h4 class="text-md font-semibold mb-4 text-surface-900 dark:text-surface-0">
                        Summary Statistics
                    </h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                        <div class="summary-stat">
                            <div class="text-xl font-bold text-green-500">
                                {{ progress.completedFeatures }}
                            </div>
                            <div class="text-xs text-surface-600 dark:text-surface-400">
                                Completed
                            </div>
                        </div>
                        <div class="summary-stat">
                            <div class="text-xl font-bold text-orange-500">
                                {{ progress.skippedFeatures }}
                            </div>
                            <div class="text-xs text-surface-600 dark:text-surface-400">
                                Skipped
                            </div>
                        </div>
                        <div class="summary-stat">
                            <div class="text-xl font-bold text-primary">
                                {{ remainingFeatures }}
                            </div>
                            <div class="text-xs text-surface-600 dark:text-surface-400">
                                Remaining
                            </div>
                        </div>
                        <div class="summary-stat">
                            <div class="text-xl font-bold text-blue-500">
                                {{ progress.estimatedTimeRemaining }}
                            </div>
                            <div class="text-xs text-surface-600 dark:text-surface-400">
                                Time Left
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// PrimeVue Components
import ProgressBar from 'primevue/progressbar';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import Tag from 'primevue/tag';

// Types
import type { OnboardingProgress, OnboardingFeature } from '../types';
import { FEATURE_DEPENDENCIES } from '../constants';

// Props
interface Props {
    progress: OnboardingProgress;
    currentFeature?: OnboardingFeature | null;
    features: OnboardingFeature[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    'step-click': [feature: OnboardingFeature];
    'toggle-view': [isFullView: boolean];
}>();

// State
const showFullView = ref(false);

// Computed
const remainingFeatures = computed(() => {
    return props.progress.totalFeatures - props.progress.completedFeatures - props.progress.skippedFeatures;
});

// Methods
const isCurrentFeature = (feature: OnboardingFeature): boolean => {
    return props.currentFeature?.id === feature.id;
};

const getStepClass = (feature: OnboardingFeature, index: number) => {
    const classes = [];

    if (feature.isCompleted) {
        classes.push('completed');
    } else if (feature.isSkipped) {
        classes.push('skipped');
    } else if (isCurrentFeature(feature)) {
        classes.push('current');
    } else {
        classes.push('pending');
    }

    if (feature.isRequired) {
        classes.push('required');
    }

    return classes.join(' ');
};

const getFeatureDependencies = (featureId: string): string[] => {
    const dependencies = FEATURE_DEPENDENCIES[featureId] || [];
    return dependencies.map(depId => {
        const depFeature = props.features.find(f => f.id === depId);
        return depFeature?.title || depId;
    });
};

const toggleFullView = () => {
    showFullView.value = !showFullView.value;
    emit('toggle-view', showFullView.value);
};

const handleStepClick = (feature: OnboardingFeature) => {
    // Only allow clicking on accessible features
    const dependencies = FEATURE_DEPENDENCIES[feature.id] || [];
    const canAccess = dependencies.every(depId => {
        const depFeature = props.features.find(f => f.id === depId);
        return depFeature?.isCompleted || depFeature?.isSkipped;
    });

    if (canAccess || feature.isCompleted || feature.isSkipped || isCurrentFeature(feature)) {
        emit('step-click', feature);
        // Close modal after clicking a step
        showFullView.value = false;
    }
};
</script>

<style scoped>
.progress-tracking {
    width: 100%;
}

/* Compact View Styles */
.compact-mode {
    background: var(--surface-0);
    border-radius: 0.5rem;
    border: 1px solid var(--surface-border);
}

.compact-progress {
    padding: 1rem;
}

.compact-header {
    width: 100%;
}

.progress-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.progress-text {
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
}

.compact-progress-bar {
    height: 0.5rem;
}

.compact-progress-bar :deep(.p-progressbar) {
    height: 0.5rem;
    background: var(--surface-200);
    border-radius: 0.25rem;
}

.compact-progress-bar :deep(.p-progressbar-value) {
    background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
    transition: width 0.6s ease;
    border-radius: 0.25rem;
}

/* Full View Modal Styles */
.full-progress-content {
    max-height: 70vh;
    overflow-y: auto;
}

/* Overall Progress */
.overall-progress {
    margin-bottom: 2rem;
}

.progress-bar-custom :deep(.p-progressbar-value) {
    background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
    transition: width 0.6s ease;
}

/* Feature Steps */
.steps-container {
    position: relative;
}

.step-item {
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.step-item:last-child {
    margin-bottom: 0;
}

.step-item:hover {
    background: var(--surface-50);
}

.dark .step-item:hover {
    background: var(--surface-800);
}

/* Step States */
.step-item.completed {
    background: var(--green-50);
    border: 1px solid var(--green-200);
}

.dark .step-item.completed {
    background: var(--green-900);
    border-color: var(--green-700);
}

.step-item.skipped {
    background: var(--orange-50);
    border: 1px solid var(--orange-200);
}

.dark .step-item.skipped {
    background: var(--orange-900);
    border-color: var(--orange-700);
}

.step-item.current {
    background: var(--primary-50);
    border: 2px solid var(--primary-200);
    animation: currentPulse 2s infinite;
}

.dark .step-item.current {
    background: var(--primary-900);
    border-color: var(--primary-700);
}

.step-item.required {
    border-left: 4px solid var(--orange-400);
}

/* Step Circle */
.step-circle {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.step-item.completed .step-circle {
    background: var(--green-500);
    border: 2px solid var(--green-600);
}

.step-item.skipped .step-circle {
    background: var(--orange-500);
    border: 2px solid var(--orange-600);
}

.step-item.current .step-circle {
    background: var(--primary-500);
    border: 2px solid var(--primary-600);
    animation: circlePulse 1.5s infinite;
}

.step-item.pending .step-circle {
    background: var(--surface-100);
    border: 2px solid var(--surface-300);
}

.dark .step-item.pending .step-circle {
    background: var(--surface-700);
    border-color: var(--surface-600);
}

.step-circle:hover {
    transform: scale(1.1);
}

.step-icon {
    font-size: 1.2rem;
}

/* Step Content */
.step-content {
    flex: 1;
    min-width: 0;
}

.step-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.step-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--surface-900);
    margin: 0;
}

.dark .step-title {
    color: var(--surface-0);
}

.step-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.step-time {
    font-size: 0.75rem;
    color: var(--surface-500);
    background: var(--surface-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.dark .step-time {
    background: var(--surface-700);
    color: var(--surface-400);
}

.step-tag {
    font-size: 0.7rem !important;
}

.step-description {
    font-size: 0.9rem;
    color: var(--surface-600);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.dark .step-description {
    color: var(--surface-400);
}

/* Step Status */
.step-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Step Dependencies */
.step-dependencies {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--surface-border);
}

/* Connector Lines */
.step-connector-before,
.step-connector-after {
    position: absolute;
    left: 2.5rem;
    width: 2px;
    background: var(--surface-300);
    transition: background-color 0.3s ease;
}

.step-connector-before {
    top: -1rem;
    height: 1rem;
}

.step-connector-after {
    bottom: -1rem;
    height: 1rem;
}

.step-connector-before.completed,
.step-connector-after.completed {
    background: var(--green-400);
}

.dark .step-connector-before,
.dark .step-connector-after {
    background: var(--surface-600);
}

.dark .step-connector-before.completed,
.dark .step-connector-after.completed {
    background: var(--green-500);
}

/* Progress Summary */
.progress-summary :deep(.p-card-content) {
    padding: 1rem;
}

.summary-stat {
    padding: 0.5rem;
}

/* Animations */
@keyframes currentPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 var(--primary-400);
    }
    50% {
        box-shadow: 0 0 0 8px transparent;
    }
}

@keyframes circlePulse {
    0%, 100% {
        box-shadow: 0 0 0 0 var(--primary-400);
    }
    50% {
        box-shadow: 0 0 0 6px transparent;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .compact-progress {
        padding: 0.75rem;
    }

    .progress-circle {
        width: 2.5rem;
        height: 2.5rem;
    }

    .progress-text {
        font-size: 0.75rem;
    }

    .compact-header .flex {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .progress-info .flex {
        justify-content: center;
    }

    .compact-actions {
        align-self: center;
    }

    /* Full view responsive */
    .full-progress-content {
        max-height: 60vh;
    }

    .step-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 0.75rem;
        padding: 1rem 0.5rem;
    }

    .step-header {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    .step-circle {
        width: 2.5rem;
        height: 2.5rem;
    }

    .step-connector-before,
    .step-connector-after {
        display: none;
    }

    .overall-progress .flex {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* Dark mode adjustments */
.dark .compact-mode {
    background: var(--surface-800);
    border-color: var(--surface-700);
}

.dark .compact-progress-bar :deep(.p-progressbar) {
    background: var(--surface-700);
}

/* Hover effects for compact view */
.compact-progress:hover {
    background: var(--surface-50);
    transition: background-color 0.2s ease;
}

.dark .compact-progress:hover {
    background: var(--surface-750);
}

/* Modal content scrollbar styling */
.full-progress-content::-webkit-scrollbar {
    width: 6px;
}

.full-progress-content::-webkit-scrollbar-track {
    background: var(--surface-100);
    border-radius: 3px;
}

.full-progress-content::-webkit-scrollbar-thumb {
    background: var(--surface-400);
    border-radius: 3px;
}

.full-progress-content::-webkit-scrollbar-thumb:hover {
    background: var(--surface-500);
}
</style>
