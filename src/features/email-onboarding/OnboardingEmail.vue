<template>
    <OnboardingLayout>
        <!-- Header -->
        <template #header>
            <OnboardingHeader title="Email Integration Setup" description="Set up email forwarding for your chatbot to handle email inquiries automatically." icon="pi pi-envelope" />
        </template>

        <!-- Steps -->
        <template #steps>
            <OnboardingSteps :steps="formattedSteps" :current-step="currentStep - 1" :allow-step-navigation="false" />
        </template>

        <!-- Main Content -->
        <OnboardingCard variant="default" size="large">
            <!-- Step 1: Email Input -->
            <div v-if="currentStep === 1" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-envelope" size="large" variant="primary" class="mb-4" />
                    <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Enter Your Email Address</h3>
                    <p class="text-surface-600 dark:text-surface-400">This will be your default email for chatbot responses and notifications.</p>
                </div>

                <div class="max-w-md mx-auto space-y-6">
                    <div class="field">
                        <label for="email" class="block font-medium mb-2 text-surface-900 dark:text-surface-0"> Email Address </label>
                        <IconField>
                            <InputIcon class="pi pi-envelope" />
                            <InputText id="email" v-model="emailForm.email" placeholder="Enter your email address" :class="{ 'p-invalid': emailForm.error }" @blur="validateEmailInput" @keyup.enter="handleStep1Continue" fluid />
                        </IconField>
                        <small v-if="emailForm.error" class="p-error">{{ emailForm.error }}</small>
                    </div>

                    <div class="flex justify-center">
                        <Button label="Continue" icon="pi pi-arrow-right" iconPos="right" @click="handleStep1Continue" :disabled="!emailForm.isValid || isLoading" :loading="isLoading" size="large" class="px-8" />
                    </div>
                </div>
            </div>

            <!-- Step 2: Email Forwarding Instructions -->
            <div v-if="currentStep === 2" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-forward" size="large" variant="primary" class="mb-4" />
                    <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Set Up Email Forwarding</h3>
                    <p class="text-surface-600 dark:text-surface-400">Forward your emails to our system so the chatbot can respond automatically.</p>
                </div>

                <OnboardingCard variant="info" size="medium" class="mb-6">
                    <div class="text-center space-y-4">
                        <div class="flex items-center justify-center gap-2">
                            <i class="pi pi-info-circle text-blue-600"></i>
                            <span class="font-medium text-blue-800 dark:text-blue-200">Forward emails to:</span>
                        </div>
                        <div class="bg-white dark:bg-surface-800 border rounded-lg px-4 py-3 font-mono text-sm">
                            {{ forwardingEmail }}
                        </div>
                        <Button icon="pi pi-copy" text size="small" @click="copyForwardingEmail" label="Copy Address" />
                    </div>
                </OnboardingCard>

                <!-- Email Provider Instructions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <OnboardingCard v-for="provider in emailProviders" :key="provider.name" variant="outlined" size="small" class="cursor-pointer hover:shadow-md transition-shadow">
                        <div @click="selectedProvider = selectedProvider === provider ? null : provider">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center gap-3">
                                    <i :class="provider.icon" class="text-2xl text-surface-600 dark:text-surface-400"></i>
                                    <span class="font-medium text-surface-900 dark:text-surface-0">{{ provider.name }}</span>
                                </div>
                                <i :class="selectedProvider === provider ? 'pi pi-chevron-up' : 'pi pi-chevron-down'" class="text-surface-500"></i>
                            </div>

                            <Transition name="slide-down">
                                <div v-if="selectedProvider === provider" class="space-y-2">
                                    <ol class="list-decimal list-inside space-y-1 text-sm text-surface-700 dark:text-surface-300">
                                        <li v-for="(instruction, index) in provider.instructions" :key="index">
                                            {{ instruction }}
                                        </li>
                                    </ol>
                                    <a v-if="provider.helpUrl" :href="provider.helpUrl" target="_blank" class="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm mt-2">
                                        <i class="pi pi-external-link mr-1"></i>
                                        Official Help Guide
                                    </a>
                                </div>
                            </Transition>
                        </div>
                    </OnboardingCard>
                </div>

                <div class="flex justify-center">
                    <Button label="I've Set Up Email Forwarding" icon="pi pi-check" @click="handleStep2Continue" severity="success" size="large" class="px-8" />
                </div>
            </div>

            <!-- Step 3: Email Validation -->
            <div v-if="currentStep === 3" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-shield" size="large" variant="primary" class="mb-4" />
                    <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Validate Email Integration</h3>
                    <p class="text-surface-600 dark:text-surface-400">We'll send a test email to verify your forwarding setup is working correctly.</p>
                </div>

                <!-- Timeout Warning -->
                <OnboardingCard v-if="showTimeoutWarning" variant="error" size="medium">
                    <div class="space-y-4">
                        <div class="flex items-start gap-3">
                            <i class="pi pi-exclamation-triangle text-red-600 mt-1"></i>
                            <div class="flex-1">
                                <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">Validation Timeout</h4>
                                <p class="text-red-700 dark:text-red-300 text-sm mb-3">We haven't received the forwarded email after 5 minutes. This usually means there's an issue with your email forwarding configuration.</p>
                                <p class="text-red-600 dark:text-red-400 text-sm mb-4">
                                    <strong>Common issues:</strong>
                                </p>
                                <ul class="text-red-600 dark:text-red-400 text-sm list-disc list-inside space-y-1 mb-4">
                                    <li>Email forwarding wasn't set up correctly</li>
                                    <li>The forwarding address was typed incorrectly</li>
                                    <li>Your email provider is blocking the forwarding</li>
                                    <li>There's a delay in your email provider's forwarding system</li>
                                </ul>
                                <div class="flex flex-col sm:flex-row gap-2">
                                    <Button label="Go Back to Setup" icon="pi pi-arrow-left" severity="danger" @click="goBackToStep2" size="small" />
                                    <Button label="Try Again" icon="pi pi-refresh" severity="secondary" @click="retryValidation" outlined size="small" />
                                </div>
                            </div>
                        </div>
                    </div>
                </OnboardingCard>

                <div v-if="!isWaitingForValidation && !showTimeoutWarning" class="text-center">
                    <Button label="Start Validation" icon="pi pi-play" @click="startEmailValidation" :loading="isValidating" size="large" class="px-8" />
                </div>

                <div v-else-if="isWaitingForValidation && !showTimeoutWarning" class="text-center space-y-4">
                    <ProgressSpinner />
                    <div class="space-y-3">
                        <h4 class="font-medium text-surface-900 dark:text-surface-0">Waiting for email validation...</h4>
                        <p class="text-surface-600 dark:text-surface-400 text-sm">
                            We've sent a test email to <strong>{{ emailForm.email }}</strong
                            >. Please check that it's forwarded to our system.
                        </p>
                        <p class="text-surface-500 dark:text-surface-400 text-xs">This may take a few minutes. The page will automatically update when validation is complete.</p>
                        <Button label="Cancel Validation" icon="pi pi-times" severity="secondary" @click="cancelValidation" outlined size="small" />
                    </div>
                </div>
            </div>

            <!-- Step 4: Email Template -->
            <div v-if="currentStep === 4" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-file-edit" size="large" variant="primary" class="mb-4" />
                    <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Configure Email Template</h3>
                    <p class="text-surface-600 dark:text-surface-400">Customize the email template that will be used when the chatbot responds to inquiries.</p>
                </div>

                <div class="space-y-6">
                    <OnboardingCard variant="warning" size="medium">
                        <div class="flex items-start gap-3">
                            <i class="pi pi-exclamation-triangle text-yellow-600 mt-1"></i>
                            <div>
                                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                                    <strong>Important:</strong> Your template must include
                                    <code class="bg-yellow-100 dark:bg-yellow-900 px-1 rounded">{{ content }}</code>
                                    where you want the chatbot's response to appear.
                                </p>
                            </div>
                        </div>
                    </OnboardingCard>

                    <div class="field">
                        <label for="template" class="block font-medium mb-2 text-surface-900 dark:text-surface-0"> Email Template </label>
                        <Editor v-model="template" editorStyle="height: 250px" placeholder="Your email template here..." @text-change="validateTemplate" />
                        <div v-if="templateValidation.errors.length" class="mt-2">
                            <small v-for="error in templateValidation.errors" :key="error" class="p-error block">
                                {{ error }}
                            </small>
                        </div>
                    </div>

                    <div class="flex justify-center gap-4">
                        <Button label="Use Default Template" icon="pi pi-refresh" severity="secondary" @click="useDefaultTemplate" outlined />
                        <Button label="Save Template" icon="pi pi-check" @click="handleStep4Continue" :disabled="!templateValidation.isValid || isLoading" :loading="isLoading" size="large" class="px-8" />
                    </div>
                </div>
            </div>

            <!-- Step 5: Complete -->
            <div v-if="currentStep === 5" class="text-center space-y-6">
                <div class="space-y-4">
                    <FeatureIcon icon="pi pi-check-circle" size="xl" variant="success" class="mb-4" />
                    <h3 class="text-2xl font-semibold text-green-700 dark:text-green-400 mb-2">Setup Complete!</h3>
                    <p class="text-surface-600 dark:text-surface-400">Your email integration is now active and ready to handle inquiries.</p>
                </div>

                <OnboardingCard variant="success" size="medium" class="max-w-md mx-auto">
                    <div class="space-y-4">
                        <h4 class="font-medium text-green-800 dark:text-green-200">What happens next?</h4>
                        <ul class="text-sm text-green-700 dark:text-green-300 space-y-2 text-left">
                            <li class="flex items-start gap-2">
                                <i class="pi pi-check text-green-600 dark:text-green-400 mt-1 text-xs"></i>
                                <span>Emails sent to your address will be forwarded to our system</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="pi pi-check text-green-600 dark:text-green-400 mt-1 text-xs"></i>
                                <span>Our AI chatbot will analyze and respond automatically</span>
                            </li>
                            <li class="flex items-start gap-2">
                                <i class="pi pi-check text-green-600 dark:text-green-400 mt-1 text-xs"></i>
                                <span>Responses will be sent using your custom template</span>
                            </li>
                        </ul>
                    </div>
                </OnboardingCard>

                <Button label="Done" icon="pi pi-home" @click="$emit('complete')" size="large" class="px-8" />
            </div>
        </OnboardingCard>
    </OnboardingLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant/store';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingSteps, OnboardingCard, FeatureIcon, type OnboardingStep } from '@/shared/components/onboarding';

// Feature components and services
import { EmailOnboardingService } from './services/emailOnboardingService';
import { EMAIL_PROVIDERS, DEFAULT_EMAIL_TEMPLATE } from './types';
import type { EmailValidationForm, TemplateValidation, EmailProvider } from './types';

// Emits
const emit = defineEmits<{
    complete: [];
}>();

// Stores
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// Services
const emailService = new EmailOnboardingService();

// State
const currentStep = ref(1);
const isLoading = ref(false);
const isValidating = ref(false);
const isWaitingForValidation = ref(false);
const selectedProvider = ref<EmailProvider | null>(null);
const template = ref(DEFAULT_EMAIL_TEMPLATE);
const emailValidationUnsubscribe = ref<(() => void) | null>(null);
const validationTimeout = ref<NodeJS.Timeout | null>(null);
const validationStartTime = ref<number | null>(null);
const showTimeoutWarning = ref(false);

// Email form
const emailForm = ref<EmailValidationForm>({
    email: '',
    isValid: false,
    error: undefined
});

// Template validation
const templateValidation = ref<TemplateValidation>({
    isValid: true,
    hasContentPlaceholder: true,
    errors: []
});

// Computed
const formattedSteps = computed<OnboardingStep[]>(() => [
    {
        id: 1,
        title: 'Email Address',
        description: 'Enter your email',
        icon: 'pi pi-envelope',
        completed: currentStep.value > 1
    },
    {
        id: 2,
        title: 'Email Forwarding',
        description: 'Set up forwarding',
        icon: 'pi pi-forward',
        completed: currentStep.value > 2
    },
    {
        id: 3,
        title: 'Validation',
        description: 'Verify setup',
        icon: 'pi pi-shield',
        completed: currentStep.value > 3
    },
    {
        id: 4,
        title: 'Template',
        description: 'Configure template',
        icon: 'pi pi-file-edit',
        completed: currentStep.value > 4
    }
]);

const emailProviders = computed(() => EMAIL_PROVIDERS);

const forwardingEmail = computed(() => {
    const userData = authStore.userData;
    if (userData?.tenantId) {
        return EmailOnboardingService.getForwardingEmail(userData.tenantId);
    }
    return '';
});

// Methods
const validateEmailInput = () => {
    const validation = EmailOnboardingService.validateEmailForm(emailForm.value.email);
    emailForm.value = validation;
};

const handleStep1Continue = async () => {
    validateEmailInput();
    if (!emailForm.value.isValid) return;

    try {
        isLoading.value = true;
        const userData = authStore.userData;
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        await emailService.saveEmailAddress(userData.tenantId, emailForm.value.email);
        currentStep.value = 2;

        toast.add({
            severity: 'success',
            summary: 'Email Saved',
            detail: 'Your email address has been saved successfully.',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving email:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save email address. Please try again.',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

const handleStep2Continue = () => {
    currentStep.value = 3;
};

const copyForwardingEmail = async () => {
    try {
        await navigator.clipboard.writeText(forwardingEmail.value);
        toast.add({
            severity: 'success',
            summary: 'Copied',
            detail: 'Forwarding email address copied to clipboard.',
            life: 2000
        });
    } catch (error) {
        console.error('Failed to copy:', error);
        toast.add({
            severity: 'warn',
            summary: 'Copy Failed',
            detail: 'Please manually copy the email address.',
            life: 3000
        });
    }
};

const startEmailValidation = async () => {
    try {
        isValidating.value = true;
        showTimeoutWarning.value = false;

        await emailService.validateEmail(emailForm.value.email);
        isWaitingForValidation.value = true;
        validationStartTime.value = Date.now();

        // Set up 5-minute timeout
        validationTimeout.value = setTimeout(
            () => {
                if (isWaitingForValidation.value) {
                    showTimeoutWarning.value = true;
                    isWaitingForValidation.value = false;

                    // Clean up subscription
                    if (emailValidationUnsubscribe.value) {
                        emailValidationUnsubscribe.value();
                        emailValidationUnsubscribe.value = null;
                    }

                    toast.add({
                        severity: 'warn',
                        summary: 'Validation Timeout',
                        detail: 'Email validation timed out after 5 minutes. Please check your forwarding setup.',
                        life: 8000
                    });
                }
            },
            5 * 60 * 1000
        ); // 5 minutes

        toast.add({
            severity: 'info',
            summary: 'Validation Started',
            detail: 'Test email sent. Please check your email forwarding.',
            life: 5000
        });
    } catch (error) {
        console.error('Error starting validation:', error);
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Failed to start email validation. Please try again.',
            life: 5000
        });
    } finally {
        isValidating.value = false;
    }
};

const validateTemplate = () => {
    templateValidation.value = EmailOnboardingService.validateEmailTemplate(template.value);
};

const useDefaultTemplate = () => {
    template.value = DEFAULT_EMAIL_TEMPLATE;
    validateTemplate();
};

const handleStep4Continue = async () => {
    validateTemplate();
    if (!templateValidation.value.isValid) return;

    try {
        isLoading.value = true;
        const userData = authStore.userData;
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        await emailService.saveEmailTemplate(userData.tenantId, template.value);
        currentStep.value = 5;

        toast.add({
            severity: 'success',
            summary: 'Template Saved',
            detail: 'Your email template has been saved successfully.',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving template:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save email template. Please try again.',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

const goBackToStep2 = () => {
    // Clean up validation state
    cleanupValidation();

    // Go back to step 2 for reconfiguration
    currentStep.value = 2;
    showTimeoutWarning.value = false;

    toast.add({
        severity: 'info',
        summary: 'Returned to Setup',
        detail: 'Please review and reconfigure your email forwarding settings.',
        life: 5000
    });
};

const retryValidation = () => {
    // Clean up previous validation attempt
    cleanupValidation();
    showTimeoutWarning.value = false;

    // Restart validation
    startEmailValidation();
};

const cancelValidation = () => {
    cleanupValidation();

    toast.add({
        severity: 'info',
        summary: 'Validation Cancelled',
        detail: 'Email validation has been cancelled.',
        life: 3000
    });
};

const cleanupValidation = () => {
    isWaitingForValidation.value = false;

    // Clear timeout
    if (validationTimeout.value) {
        clearTimeout(validationTimeout.value);
        validationTimeout.value = null;
    }

    // Clean up subscription
    if (emailValidationUnsubscribe.value) {
        emailValidationUnsubscribe.value();
        emailValidationUnsubscribe.value = null;
    }

    validationStartTime.value = null;
};

// Initialize component
const initializeComponent = async () => {
    try {
        const userData = await authStore.getUserData();
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        const tenant = await tenantStore.getTenant(userData.tenantId);
        if (!tenant) {
            throw new Error('Tenant not found');
        }

        // Set up real-time listener for email validation changes
        emailValidationUnsubscribe.value = tenantStore.subscribeToEmailValidation(userData.tenantId, (validated: boolean, tenantData) => {
            // Check if email validation status changed to true
            if (validated && (isWaitingForValidation.value || showTimeoutWarning.value)) {
                isWaitingForValidation.value = false;
                showTimeoutWarning.value = false;
                currentStep.value = 4;

                // Clear timeout if it's still active
                if (validationTimeout.value) {
                    clearTimeout(validationTimeout.value);
                    validationTimeout.value = null;
                }

                // Clean up the subscription since validation is complete
                if (emailValidationUnsubscribe.value) {
                    emailValidationUnsubscribe.value();
                    emailValidationUnsubscribe.value = null;
                }

                toast.add({
                    severity: 'success',
                    summary: 'Email Validated',
                    detail: 'Your email forwarding has been successfully validated!',
                    life: 5000
                });
            }
        });

        // Initialize form with existing data
        if (tenant.email?.defaultEmail) {
            emailForm.value.email = tenant.email.defaultEmail;
            emailForm.value.isValid = true;
        }

        if (tenant.email?.template) {
            template.value = tenant.email.template;
        }

        // Set current step based on existing data
        currentStep.value = EmailOnboardingService.getCurrentStep(tenant);

        // If already waiting for validation, set the flag
        if (tenant.email?.defaultEmail && !tenant.email?.validated && currentStep.value === 3) {
            isWaitingForValidation.value = true;
        }
    } catch (error) {
        console.error('Error initializing component:', error);
        toast.add({
            severity: 'error',
            summary: 'Initialization Error',
            detail: 'Failed to load email numbers-prompts-onboarding data.',
            life: 5000
        });
    }
};

// Lifecycle
onMounted(() => {
    initializeComponent();
    validateTemplate();
});

onUnmounted(() => {
    // Clean up all validation resources
    cleanupValidation();
});

// Watch for template changes
watch(template, () => {
    validateTemplate();
});
</script>

<style scoped>
/* Custom styles for email onboarding specific elements */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.field {
    margin-bottom: 1rem;
}

/* Slide down animation for provider instructions */
.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s ease;
    overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
    max-height: 0;
    opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
    max-height: 500px;
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .flex.gap-4 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .grid.grid-cols-1.md\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}
</style>
