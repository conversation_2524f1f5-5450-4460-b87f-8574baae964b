import { User as UserApp } from '@/entities/user';
import { User } from 'firebase/auth';
import { requestDeleteUserFirebase, requestDeleteUsersFirebase, requestSaveUserFirebase, UserDeleteParam, UserDeleteParams } from '@/shared';

export async function requestSaveCardDetails(params: any) {
    const { data } = await requestSaveCardDetailsFirebase(params);
    return data as User;
}
export async function requestPaySubscription(params: any) {
    const { data } = await requestPaySubscriptionFirebase(params);
    return data as boolean;
}
export async function requestCancelSubscription(params: any) {
    const { data } = await requestCancelSubscriptionFirebase(params);
    return data as boolean;
}
