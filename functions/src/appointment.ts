import { googleDriveFolderId, googleServiceAccountEmail, googleServiceAccountKey, openAIKey } from './utils';
import { createGoogleCalendar, processLeadAppointmentCalendar, processLeadCheckAvailableAppointmentSlots, renewGoogleCalendateEventWebhook } from './appointments';
import * as logger from 'firebase-functions/logger';
import { onSchedule } from 'firebase-functions/v2/scheduler';
import { HttpsError, onCall } from 'firebase-functions/v2/https';

export const onRenewGoogleCalendarEventWebhook = onSchedule(
    {
        schedule: '0 0 * * 0',
        secrets: [googleServiceAccountKey, googleServiceAccountEmail, googleDriveFolderId],
        cpu: 4,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async () => {
        try {
            await renewGoogleCalendateEventWebhook();
            logger.info('Finished renewing Google Calendar event webhook.');
            return;
        } catch (error) {
            logger.error('Error renewing Google Calendar event webhook:', error);
            throw new HttpsError('internal', 'Error renewing Google Calendar event webhook.');
        }
    }
);

// Cloud function to add calendar event
export const addAppointmentEvent = onCall(
    {
        cors: true,
        secrets: [googleServiceAccountKey, googleServiceAccountEmail, openAIKey, googleDriveFolderId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        try {
            const { data } = request;
            const { lead } = data;
            const { name, address, tenantId } = lead;

            // Validate required fields
            if (!tenantId) {
                throw new Error('Missing required fields: tenantId');
            }
            if (data?.action !== 'cancel') {
                if (!name || !address) {
                    throw new Error('Missing required fields: name or address');
                }
            }

            await processLeadAppointmentCalendar(data);
            return true;
        } catch (error) {
            logger.error('Error creating event:', error);
            throw new Error('Failed to create event');
        }
    }
);

export const onCreateCalendar = onCall(
    {
        cors: true,
        secrets: [googleServiceAccountKey, googleServiceAccountEmail, googleDriveFolderId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        try {
            if (!request.data.calendarName || !request.data.tenantId) {
                throw new Error('Missing required fields: calendarName or tenantId');
            }
            return await createGoogleCalendar(request.data.calendarName, request.data.tenantId, request.data.timezone);
        } catch (error) {
            logger.error('Error creating calendar:', error);
        }
    }
);

// Cloud function to check available appointment slots
export const checkAvailableSlots = onCall(
    {
        cors: true,
        secrets: [googleServiceAccountKey, googleServiceAccountEmail, openAIKey, googleDriveFolderId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        try {
            const { data } = request;
            const { preferredAppointmentDate, selectedLocation } = data;

            // Validate required fields
            if (!preferredAppointmentDate || !selectedLocation) {
                throw new Error('Missing required fields: preferredAppointmentDate');
            }

            const result = await processLeadCheckAvailableAppointmentSlots(data);
            return result;
        } catch (error) {
            logger.error('Error checking available slots:', error);
            throw new Error('Failed to check available slots');
        }
    }
);
