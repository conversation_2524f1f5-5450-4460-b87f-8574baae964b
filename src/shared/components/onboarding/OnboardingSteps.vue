<template>
    <div class="onboarding-steps">
        <div class="steps-container">
            <!-- Progress Line -->
            <div class="progress-line">
                <div 
                    class="progress-fill" 
                    :style="{ width: `${progressPercentage}%` }"
                ></div>
            </div>

            <!-- Step Items -->
            <div 
                v-for="(step, index) in steps" 
                :key="step.id || index"
                class="step-item"
                :class="getStepClasses(index)"
                @click="handleStepClick(index)"
            >
                <!-- Step Circle -->
                <div class="step-circle">
                    <i v-if="isStepCompleted(index)" class="pi pi-check step-icon"></i>
                    <i v-else-if="step.icon" :class="step.icon" class="step-icon"></i>
                    <span v-else class="step-number">{{ index + 1 }}</span>
                </div>

                <!-- Step Content -->
                <div class="step-content">
                    <div class="step-title">{{ step.title }}</div>
                    <div v-if="step.description" class="step-description">
                        {{ step.description }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Types
interface OnboardingStep {
    id?: string | number;
    title: string;
    description?: string;
    icon?: string;
    completed?: boolean;
    disabled?: boolean;
}

// Props
interface Props {
    steps: OnboardingStep[];
    currentStep: number;
    allowStepNavigation?: boolean;
    showProgress?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    allowStepNavigation: true,
    showProgress: true
});

// Emits
const emit = defineEmits<{
    'step-click': [stepIndex: number];
}>();

// Computed
const progressPercentage = computed(() => {
    if (props.steps.length <= 1) return 0;
    return (props.currentStep / (props.steps.length - 1)) * 100;
});

// Methods
const isStepCompleted = (index: number): boolean => {
    return props.steps[index]?.completed || index < props.currentStep;
};

const isStepActive = (index: number): boolean => {
    return index === props.currentStep;
};

const isStepDisabled = (index: number): boolean => {
    return props.steps[index]?.disabled || (!props.allowStepNavigation && index !== props.currentStep);
};

const getStepClasses = (index: number) => {
    return {
        'step-active': isStepActive(index),
        'step-completed': isStepCompleted(index),
        'step-disabled': isStepDisabled(index),
        'step-clickable': props.allowStepNavigation && !isStepDisabled(index)
    };
};

const handleStepClick = (index: number) => {
    if (!isStepDisabled(index) && props.allowStepNavigation) {
        emit('step-click', index);
    }
};
</script>

<style scoped>
.onboarding-steps {
    @apply w-full mb-8;
}

.steps-container {
    @apply relative flex items-start justify-between;
    @apply flex-col gap-6 md:flex-row md:gap-4;
}

.progress-line {
    @apply absolute top-6 left-0 w-full h-0.5 bg-surface-200 dark:bg-surface-700;
    @apply hidden md:block;
}

.progress-fill {
    @apply h-full bg-primary-500 transition-all duration-500 ease-out;
}

.step-item {
    @apply relative flex flex-col items-center text-center;
    @apply flex-1 min-w-0 transition-all duration-200;
}

.step-item.step-clickable {
    @apply cursor-pointer;
}

.step-item.step-clickable:hover .step-circle {
    @apply transform scale-105;
}

.step-circle {
    @apply relative z-10 w-12 h-12 rounded-full flex items-center justify-center;
    @apply text-sm font-semibold transition-all duration-200;
    @apply bg-surface-200 dark:bg-surface-700 text-surface-600 dark:text-surface-400;
    @apply border-4 border-white dark:border-surface-900;
}

.step-item.step-active .step-circle {
    @apply bg-primary-500 text-white shadow-lg;
}

.step-item.step-completed .step-circle {
    @apply bg-green-500 text-white;
}

.step-item.step-disabled .step-circle {
    @apply opacity-50;
}

.step-icon {
    @apply text-lg;
}

.step-number {
    @apply text-sm font-bold;
}

.step-content {
    @apply mt-3 max-w-32;
}

.step-title {
    @apply text-sm font-medium transition-colors duration-200;
    @apply text-surface-600 dark:text-surface-400;
}

.step-item.step-active .step-title {
    @apply text-primary-600 dark:text-primary-400;
}

.step-item.step-completed .step-title {
    @apply text-green-600 dark:text-green-400;
}

.step-description {
    @apply text-xs text-surface-500 dark:text-surface-400 mt-1;
    @apply leading-tight;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .steps-container {
        @apply flex-col gap-4;
    }
    
    .step-item {
        @apply flex-row text-left items-center;
        @apply w-full;
    }
    
    .step-content {
        @apply mt-0 ml-4 max-w-none flex-1;
    }
    
    .step-circle {
        @apply flex-shrink-0;
    }
}
</style>
