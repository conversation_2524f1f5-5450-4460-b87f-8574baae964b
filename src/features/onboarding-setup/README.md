# Onboarding Setup Feature

A comprehensive onboarding system that guides users through setting up their SaaS platform by integrating all available features in an optimal order.

## Overview

The Onboarding Setup feature provides a streamlined, user-friendly way to configure all aspects of the SaaS platform. It integrates with existing feature modules and presents them in a logical, dependency-aware sequence.

## Features

- **🎯 Smart Feature Ordering**: Automatically orders features based on dependencies and importance
- **📊 Progress Tracking**: Real-time progress visualization with time estimates
- **🎨 Feature Selection**: Allow users to choose which features to set up
- **📱 Responsive Design**: Fully responsive across all devices using PrimeVue components
- **🔄 Flexible Navigation**: Skip, retry, or reconfigure features as needed
- **📈 Dashboard View**: Overview of setup progress and quick access to features
- **⚡ Modern UI**: Clean, modern interface with smooth animations

## Integrated Features

The onboarding system integrates the following features in optimal order:

### Essential Setup (Required)
1. **Company Information** - Business profile and contact details
2. **User Profile** - Personal information and account preferences

### Communication Features
3. **Google Calendar Integration** - Appointment booking and management
4. **Communication Services** - SMS and call integration (Twilio/Bland.AI)
5. **Email Integration** - Email forwarding and chatbot integration

### AI & Automation
6. **AI Prompt Configuration** - Chatbot and analysis prompts
7. **AI Scripts Setup** - Personalized communication scripts

### Advanced Configuration
8. **Lead Configuration** - Lead actions, sources, and statuses

## Components

### Main Components

- **`OnboardingSetup.vue`** - Main orchestrator component
- **`FeatureSelection.vue`** - Feature selection interface
- **`ProgressTracking.vue`** - Progress visualization component
- **`OnboardingDashboard.vue`** - Setup overview dashboard

### Services

- **`OnboardingService`** - Core onboarding logic and state management
- **`ProgressTrackingService`** - Progress calculation and tracking
- **`FeatureIntegrationService`** - Feature component integration

## Usage

### Basic Usage

```vue
<template>
  <OnboardingSetup
    :auto-start="false"
    redirect-on-complete="/dashboard"
    @complete="handleOnboardingComplete"
    @exit="handleExit"
  />
</template>

<script setup>
import { OnboardingSetup } from '@/features/onboarding-setup';

const handleOnboardingComplete = (data) => {
  console.log('Onboarding completed:', data);
};

const handleExit = () => {
  console.log('User exited onboarding');
};
</script>
```

### With Pre-selected Features

```vue
<template>
  <OnboardingSetup
    :auto-start="true"
    :selected-features="['companyInfo', 'calendar', 'prompts']"
    @feature-complete="handleFeatureComplete"
  />
</template>
```

### Dashboard Only

```vue
<template>
  <OnboardingDashboard
    :features="onboardingFeatures"
    :started-at="startDate"
    @continue-onboarding="resumeOnboarding"
    @start-feature="startSpecificFeature"
  />
</template>
```

## Configuration

### Feature Configuration

Features are configured in `constants.ts`:

```typescript
export const ONBOARDING_FEATURES = {
  companyInfo: {
    title: 'Company Information',
    description: 'Set up your company profile...',
    icon: 'pi pi-building',
    category: 'essential',
    estimatedTime: '5 minutes',
    isRequired: true
  },
  // ... other features
};
```

### Feature Dependencies

Dependencies are defined to ensure proper setup order:

```typescript
export const FEATURE_DEPENDENCIES = {
  scripts: ['prompts'],        // Scripts depend on prompts
  leadConfig: ['companyInfo'], // Lead config needs company info
  email: ['companyInfo'],      // Email setup needs company info
  numbers: ['companyInfo']     // Phone setup needs company info
};
```

## API Reference

### OnboardingSetup Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `autoStart` | `boolean` | `false` | Start onboarding immediately |
| `selectedFeatures` | `string[]` | `undefined` | Pre-selected features |
| `redirectOnComplete` | `string` | `'/dashboard'` | Redirect URL after completion |

### OnboardingSetup Events

| Event | Payload | Description |
|-------|---------|-------------|
| `complete` | `{ completedFeatures, progress }` | Onboarding completed |
| `exit` | `void` | User exited onboarding |
| `featureComplete` | `featureId, data` | Individual feature completed |

### OnboardingService Methods

```typescript
// Start onboarding with optional feature selection
await onboardingService.startOnboarding(['companyInfo', 'calendar']);

// Complete a feature
await onboardingService.completeFeature('companyInfo', formData);

// Skip a feature
await onboardingService.skipFeature('email');

// Navigate between features
await onboardingService.nextStep();
await onboardingService.previousStep();
await onboardingService.goToFeature('calendar');
```

## Styling

The components use PrimeVue's design system with custom CSS variables:

```css
/* Custom progress bar styling */
.progress-bar-custom :deep(.p-progressbar-value) {
  background: linear-gradient(90deg, var(--primary-500), var(--primary-400));
}

/* Feature card states */
.feature-card.selected :deep(.p-card) {
  border: 2px solid var(--primary-color);
  background: var(--primary-50);
}
```

## Responsive Design

The onboarding system is fully responsive:

- **Desktop**: Full-width layout with side-by-side content
- **Tablet**: Stacked layout with optimized spacing
- **Mobile**: Single-column layout with touch-friendly interactions

## Accessibility

- Keyboard navigation support
- Screen reader friendly
- High contrast mode support
- Focus management
- ARIA labels and descriptions

## Integration with Existing Features

### Component Mapping System

The onboarding system uses a dynamic component mapping system defined in `constants.ts`:

```typescript
// COMPONENT_MAP maps feature IDs to Vue component names
export const COMPONENT_MAP: Record<string, string> = {
  companyInfo: 'CompanyInfoUpdate',
  userInfo: 'CompanyUserUpdate',
  calendar: 'OnboardingCalendar',
  numbers: 'OnboardingNumber',
  email: 'OnboardingEmail',
  prompts: 'OnboardingPrompts',
  scripts: 'OnboardingScripts',
  leadConfig: 'OnboardingLeadConfiguration'
};
```

### How Components Are Loaded

The `FeatureIntegrationService` uses this mapping to dynamically load components:

```typescript
// 1. Component registry with actual Vue components
private componentRegistry = {
  CompanyInfoUpdate: markRaw(CompanyInfoUpdate),
  OnboardingCalendar: markRaw(OnboardingCalendar),
  // ... other imports
};

// 2. Dynamic mapping using COMPONENT_MAP
Object.entries(COMPONENT_MAP).forEach(([featureId, componentName]) => {
  const component = this.componentRegistry[componentName];
  if (component) {
    this.componentMap[featureId] = component;
  }
});

// 3. Component retrieval
getFeatureComponent(featureId: string) {
  return this.componentMap[featureId] || null;
}
```

### Adding New Features

To add a new feature to the onboarding system:

1. **Add to COMPONENT_MAP**:
```typescript
export const COMPONENT_MAP = {
  // ... existing mappings
  newFeature: 'NewFeatureComponent'
};
```

2. **Add to ONBOARDING_FEATURES**:
```typescript
export const ONBOARDING_FEATURES = {
  // ... existing features
  newFeature: {
    title: 'New Feature Setup',
    description: 'Configure your new feature',
    icon: 'pi pi-star',
    category: 'configuration',
    estimatedTime: '5 minutes',
    isRequired: false
  }
};
```

3. **Import in FeatureIntegrationService**:
```typescript
import NewFeatureComponent from '@/features/new-feature/NewFeatureComponent.vue';

// Add to componentRegistry
this.componentRegistry = {
  // ... existing components
  NewFeatureComponent: markRaw(NewFeatureComponent)
};
```

### Event Handling

Each component gets standardized event handlers:

```typescript
const eventHandlers = {
  companyInfo: {
    'update:completed': onComplete,
    'error': onError
  },
  calendar: {
    'complete': onComplete,
    'error': onError
  }
};
```

## Development

### Adding New Features

1. Add feature configuration to `constants.ts`
2. Update component mapping in `FeatureIntegrationService`
3. Define dependencies if needed
4. Test integration

### Customizing UI

The onboarding system uses PrimeVue components and can be customized through:

- CSS custom properties
- Component props
- Theme configuration
- Custom styling

## Best Practices

1. **Feature Order**: Place essential features first
2. **Dependencies**: Define clear dependencies between features
3. **Progress Feedback**: Provide clear progress indicators
4. **Error Handling**: Handle errors gracefully with retry options
5. **Mobile First**: Design for mobile devices first
6. **Accessibility**: Ensure keyboard and screen reader support

## Examples

See the `examples/` directory for complete implementation examples and integration patterns.
