export interface OnboardingFeature {
    id: string;
    name: string;
    title: string;
    description: string;
    icon: string;
    color?: string;
    category: 'essential' | 'communication' | 'automation' | 'configuration';
    priority: number;
    estimatedTime: string;
    dependencies?: string[];
    component: string;
    isRequired: boolean;
    isCompleted: boolean;
    isSkipped: boolean;
    completedAt?: Date;
    data?: any;
}

export interface OnboardingStep {
    id: string;
    featureId: string;
    title: string;
    description: string;
    icon?: string;
    isCompleted: boolean;
    isActive: boolean;
    isSkipped: boolean;
    order: number;
}

export interface OnboardingProgress {
    totalFeatures: number;
    completedFeatures: number;
    skippedFeatures: number;
    currentFeature?: string;
    progressPercentage: number;
    estimatedTimeRemaining: string;
    startedAt: Date;
    lastUpdatedAt: Date;
}

export interface FeatureConfig {
    title: string;
    description: string;
    icon: string;
    color?: string;
    category: string;
    estimatedTime: string;
    isRequired: boolean;
}

export interface OnboardingState {
    isActive: boolean;
    currentFeatureId?: string;
    currentStepIndex: number;
    features: OnboardingFeature[];
    progress: OnboardingProgress;
    selectedFeatures: string[];
    completedFeatures: string[];
    skippedFeatures: string[];
    isLoading: boolean;
    error?: string;
}

export interface FeatureStatus {
    id: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'skipped' | 'error';
    progress: number;
    lastUpdated: Date;
    error?: string;
    data?: any;
}

export interface OnboardingOptions {
    autoAdvance: boolean;
    allowSkipping: boolean;
    showProgress: boolean;
    showEstimatedTime: boolean;
    enableFeatureSelection: boolean;
    redirectOnComplete?: string;
    onComplete?: () => void;
    onFeatureComplete?: (featureId: string, data: any) => void;
    onSkip?: (featureId: string) => void;
    onError?: (error: string, featureId?: string) => void;
}

export interface FeatureCategory {
    id: string;
    name: string;
    title: string;
    description: string;
    icon: string;
    color: string;
    order: number;
}

export interface OnboardingEvent {
    type: 'feature_started' | 'feature_completed' | 'feature_skipped' | 'onboarding_completed' | 'error';
    featureId?: string;
    timestamp: Date;
    data?: any;
    error?: string;
}
