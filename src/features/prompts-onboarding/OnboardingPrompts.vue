<template>
    <OnboardingLayout maxWidth="2xl" background="surface">
        <!-- Header -->
        <template #header>
            <OnboardingHeader title="AI Prompt Configuration" description="Configure how your AI assistants interact with customers and analyze leads" icon="pi pi-sparkles">
                <template #actions>
                    <div class="flex items-center gap-3">
                        <div v-if="overallSaveStatus" class="flex items-center gap-2 text-sm">
                            <i
                                :class="[
                                    overallSaveStatus === 'saving'
                                        ? 'pi pi-spin pi-spinner text-blue-500'
                                        : overallSaveStatus === 'saved'
                                          ? 'pi pi-check text-green-500'
                                          : overallSaveStatus === 'error'
                                            ? 'pi pi-exclamation-triangle text-red-500'
                                            : 'pi pi-circle text-surface-400'
                                ]"
                            ></i>
                            <span :class="[overallSaveStatus === 'saving' ? 'text-blue-600' : overallSaveStatus === 'saved' ? 'text-green-600' : overallSaveStatus === 'error' ? 'text-red-600' : 'text-surface-500']">
                                {{ overallSaveStatusText }}
                            </span>
                        </div>
                        <Button label="Save All" icon="pi pi-save" :disabled="!hasChanges || isSaving" :loading="isSaving" @click="saveAll" />
                    </div>
                </template>
            </OnboardingHeader>
        </template>

        <!-- Steps -->
        <template #steps>
            <OnboardingSteps :steps="formattedSteps" :current-step="currentStep" :allow-step-navigation="true" @step-click="goToStep" />
        </template>

        <!-- Main Content -->
        <OnboardingCard :title="stepItems[currentStep]?.title" :subtitle="stepItems[currentStep]?.description" :icon="stepItems[currentStep]?.icon" variant="default" size="large">
            <!-- Step 1: Chatbot Prompt -->
            <ChatbotPromptEditor v-if="currentStep === 0" ref="chatbotEditorRef" v-model="chatbotConfig" :tenant-id="tenantId" :auto-save="autoSaveEnabled" @save="onChatbotSave" @validate="onChatbotValidate" />

            <!-- Step 2: Lead Analysis Prompt -->
            <LeadAnalysisPromptEditor v-else-if="currentStep === 1" ref="analysisEditorRef" v-model="analysisConfig" :tenant-id="tenantId" :auto-save="autoSaveEnabled" @save="onAnalysisSave" @validate="onAnalysisValidate" />

            <!-- Step 3: Follow-up Prompt -->
            <FollowupPromptEditor v-else-if="currentStep === 2" ref="followupEditorRef" v-model="followupConfig" :tenant-id="tenantId" :auto-save="autoSaveEnabled" @save="onFollowupSave" @validate="onFollowupValidate" />

            <!-- Navigation Footer -->
            <template #footer>
                <div class="flex justify-between items-center">
                    <Button label="Previous" icon="pi pi-arrow-left" outlined :disabled="currentStep === 0" @click="previousStep" />

                    <div class="text-sm text-surface-500 dark:text-surface-400">Step {{ currentStep + 1 }} of {{ stepItems.length }}</div>

                    <Button v-if="currentStep < stepItems.length - 1" label="Next" icon="pi pi-arrow-right" iconPos="right" :disabled="!isCurrentStepValid" @click="nextStep" />
                    <Button v-else label="Complete Setup" icon="pi pi-check" iconPos="right" :disabled="!allStepsValid" @click="completeSetup" />
                </div>
            </template>
        </OnboardingCard>
    </OnboardingLayout>

    <!-- Completion Dialog -->
    <Dialog v-model:visible="showCompletionDialog" modal header="Setup Complete!" :style="{ width: '450px' }" :closable="false">
        <div class="text-center py-4 space-y-4">
            <FeatureIcon icon="pi pi-check" size="xl" variant="success" class="mx-auto" />
            <div>
                <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">AI Prompts Configured Successfully!</h3>
                <p class="text-surface-600 dark:text-surface-400 mb-4">Your AI assistants are now ready to interact with customers, analyze leads, and generate follow-up messages.</p>
            </div>
            <div class="flex justify-center gap-3">
                <Button label="View Dashboard" icon="pi pi-home" @click="goToDashboard" />
                <Button label="Test AI Responses" icon="pi pi-play" outlined @click="testAI" />
            </div>
        </div>
    </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingSteps, OnboardingCard, FeatureIcon, type OnboardingStep } from '@/shared/components/onboarding';

// Feature components
import ChatbotPromptEditor from './components/ChatbotPromptEditor.vue';
import LeadAnalysisPromptEditor from './components/LeadAnalysisPromptEditor.vue';
import FollowupPromptEditor from './components/FollowupPromptEditor.vue';

import { useAuthStore } from '@/entities/auth/store/authStore';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import { useReactiveAutoSave } from './services/autoSaveService';

import type { ChatPromptConfig, AnalysisPromptConfig, FollowupPromptConfig, OnboardingStepPrompts } from './types';
import { ONBOARDING_STEPS, DEFAULT_OPENAI_PARAMETERS } from './types';

// Props
interface Props {
    autoSaveEnabled?: boolean;
    completionRedirect?: string;
}

const props = withDefaults(defineProps<Props>(), {
    autoSaveEnabled: true,
    completionRedirect: '/dashboard'
});

// Emits
const emit = defineEmits<{
    complete: [data: any];
    'step-change': [step: number];
}>();

// Stores and services
const router = useRouter();
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// Get tenant ID
const tenantId = computed(() => authStore.userData?.tenantId || '');

// Auto-save service
const { saveStatus, autoSave, cancelAll } = useReactiveAutoSave(tenantId.value);

// Local state
const currentStep = ref(0);
const showCompletionDialog = ref(false);
const isSaving = ref(false);

// Step validation states
const stepValidation = ref({
    chatbot: false,
    analysis: false,
    followup: false
});

// Configuration states
const chatbotConfig = ref<ChatPromptConfig>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

const analysisConfig = ref<AnalysisPromptConfig>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

const followupConfig = ref<FollowupPromptConfig & { prompt?: string }>({
    prompt: '',
    ...DEFAULT_OPENAI_PARAMETERS
});

// Component refs
const chatbotEditorRef = ref();
const analysisEditorRef = ref();
const followupEditorRef = ref();

// Computed
const stepItems = computed<OnboardingStepPrompts[]>(() =>
    ONBOARDING_STEPS.map((step, index) => ({
        ...step,
        completed: index < currentStep.value || (index === currentStep.value && isCurrentStepValid.value)
    }))
);

const formattedSteps = computed<OnboardingStepPrompts[]>(() =>
    stepItems.value.map((step, index) => ({
        id: step.id,
        title: step.title,
        description: step.description,
        icon: step.icon,
        completed: step.completed
    }))
);

const isCurrentStepValid = computed(() => {
    switch (currentStep.value) {
        case 0:
            return stepValidation.value.chatbot;
        case 1:
            return stepValidation.value.analysis;
        case 2:
            return stepValidation.value.followup;
        default:
            return false;
    }
});

const allStepsValid = computed(() => stepValidation.value.chatbot && stepValidation.value.analysis && stepValidation.value.followup);

const hasChanges = computed(() => chatbotConfig.value.prompt.length > 0 || analysisConfig.value.prompt.length > 0 || followupConfig.value.prompt?.length > 0);

const overallSaveStatus = computed(() => {
    const statuses = Object.values(saveStatus.value);
    if (statuses.includes('saving')) return 'saving';
    if (statuses.includes('error')) return 'error';
    if (statuses.includes('saved')) return 'saved';
    return 'idle';
});

const overallSaveStatusText = computed(() => {
    switch (overallSaveStatus.value) {
        case 'saving':
            return 'Saving changes...';
        case 'saved':
            return 'All changes saved';
        case 'error':
            return 'Save failed';
        default:
            return '';
    }
});

// Methods
const goToStep = (step: number) => {
    if (step >= 0 && step < stepItems.value.length) {
        currentStep.value = step;
        emit('step-change', step);
    }
};

const nextStep = () => {
    if (currentStep.value < stepItems.value.length - 1 && isCurrentStepValid.value) {
        currentStep.value++;
        emit('step-change', currentStep.value);
    }
};

const previousStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
        emit('step-change', currentStep.value);
    }
};

// Save handlers
const onChatbotSave = (config: ChatPromptConfig) => {
    autoSave.chatPrompt(config);
};

const onAnalysisSave = (config: AnalysisPromptConfig) => {
    autoSave.analysisPrompt(config);
};

const onFollowupSave = (config: FollowupPromptConfig & { prompt?: string }) => {
    autoSave.followupPrompt(config);
};

// Validation handlers
const onChatbotValidate = (isValid: boolean) => {
    stepValidation.value.chatbot = isValid;
};

const onAnalysisValidate = (isValid: boolean) => {
    stepValidation.value.analysis = isValid;
};

const onFollowupValidate = (isValid: boolean) => {
    stepValidation.value.followup = isValid;
};

const saveAll = async () => {
    try {
        isSaving.value = true;

        const updateData = {
            chat: { prompt: chatbotConfig.value },
            analysis: { prompt: analysisConfig.value },
            followup: followupConfig.value
        };

        await tenantStore.updateTenant(tenantId.value, updateData);

        toast.add({
            severity: 'success',
            summary: 'Saved',
            detail: 'All prompt configurations saved successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Save failed:', error);
        toast.add({
            severity: 'error',
            summary: 'Save Failed',
            detail: 'Failed to save configurations. Please try again.',
            life: 5000
        });
    } finally {
        isSaving.value = false;
    }
};

const completeSetup = async () => {
    if (!allStepsValid.value) return;

    await saveAll();
    showCompletionDialog.value = true;

    emit('complete', {
        chatbot: chatbotConfig.value,
        analysis: analysisConfig.value,
        followup: followupConfig.value
    });
};

const goToDashboard = () => {
    router.push(props.completionRedirect);
};

const testAI = () => {
    // Navigate to AI testing page or open test dialog
    router.push('/leads'); // or wherever AI testing is available
};

// Load existing configurations
const loadExistingConfigs = async () => {
    try {
        const tenant = await tenantStore.getTenant(tenantId.value);
        if (tenant) {
            if (tenant.chat?.prompt) {
                chatbotConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.chat.prompt };
            }
            if (tenant.analysis?.prompt) {
                analysisConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.analysis.prompt };
            }
            if (tenant.followup) {
                followupConfig.value = { ...DEFAULT_OPENAI_PARAMETERS, ...tenant.followup };
            }
        }
    } catch (error) {
        console.error('Failed to load existing configurations:', error);
    }
};

// Lifecycle
onMounted(() => {
    if (tenantId.value) {
        loadExistingConfigs();
    }
});

onUnmounted(() => {
    cancelAll();
});
</script>

<style scoped>
/* Custom styles for prompts onboarding specific elements */
.space-y-4 > * + * {
    margin-top: 1rem;
}

/* Dialog content styling */
:deep(.p-dialog-content) {
    padding: 0;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .flex.gap-3 .p-button {
        width: 100%;
    }
}
</style>
