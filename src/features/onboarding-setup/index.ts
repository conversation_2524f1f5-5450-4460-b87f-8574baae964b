// Main feature exports
export { default as OnboardingSetup } from './OnboardingSetup.vue';
export { default as FeatureSelection } from './components/FeatureSelection.vue';
export { default as ProgressTracking } from './components/ProgressTracking.vue';
export { default as OnboardingDashboard } from './components/OnboardingDashboard.vue';

// Service exports
export { OnboardingService } from './services/OnboardingService';
export { ProgressTrackingService } from './services/ProgressTrackingService';
export { FeatureIntegrationService } from './services/FeatureIntegrationService';

// Type exports
export type {
    OnboardingFeature,
    OnboardingStep,
    OnboardingProgress,
    FeatureConfig,
    OnboardingState,
    FeatureStatus,
    OnboardingOptions
} from './types';

// Constants exports
export {
    ONBOARDING_FEATURES,
    FEATURE_ORDER,
    DEFAULT_ONBOARDING_OPTIONS
} from './constants';
