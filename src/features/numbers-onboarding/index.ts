// Main feature exports
export { default as OnboardingNumber } from './OnboardingNumber.vue';

// Components
export { default as PhoneNumberInput } from './components/PhoneNumberInput.vue';
export { default as ServiceConfiguration } from './components/ServiceConfiguration.vue';
export { default as NumberPurchase } from './components/NumberPurchase.vue';
export { default as ValidationProcess } from './components/ValidationProcess.vue';

// Services
export { NumberPurchaseService } from './services/numberPurchaseService';
export { useNumberConfigService, NumberConfigService } from './services/numberConfigService';

// Types
export type { PhoneNumberInput as PhoneNumberInputType, ServiceConfiguration as ServiceConfigurationType, NumberPurchaseRequest, NumberPurchaseResponse, ValidationStatus, OnboardingStep, OnboardingState } from './types';

export { ONBOARDING_STEPS } from './types';
