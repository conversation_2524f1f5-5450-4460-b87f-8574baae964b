<template>
    <Dialog :visible="visible" @update:visible="$emit('update:visible', $event)" modal header="Script Examples" :style="{ width: '70rem' }" :breakpoints="{ '1199px': '85vw', '575px': '95vw' }">
        <div class="script-examples">
            <!-- Filter by Lead Action -->
            <div class="filter-section mb-4">
                <div class="flex align-items-center gap-3">
                    <label class="font-semibold text-900">Filter by Lead Action:</label>
                    <Dropdown v-model="selectedAction" :options="actionOptions" option-label="label" option-value="key" placeholder="All Actions" class="w-12rem" show-clear />
                </div>
            </div>

            <!-- Examples Grid -->
            <div class="examples-grid">
                <div v-for="template in filteredTemplates" :key="template.actionKey" class="example-card">
                    <Card>
                        <template #header>
                            <div class="card-header p-3 border-bottom-1 border-200">
                                <div class="flex justify-content-between align-items-center">
                                    <div class="flex align-items-center gap-2">
                                        <div class="action-badge flex align-items-center gap-2 px-2 py-1 border-round text-sm" :style="getActionStyle(template.actionKey)">
                                            <i :class="getActionIcon(template.actionKey)"></i>
                                            <span class="font-semibold">{{ getActionLabel(template.actionKey) }}</span>
                                        </div>
                                    </div>
                                    <Button label="Use Template" size="small" @click="useTemplate(template)" />
                                </div>
                            </div>
                        </template>

                        <template #content>
                            <div class="example-content">
                                <h4 class="text-lg font-semibold mb-2">{{ template.title }}</h4>
                                <p class="text-600 text-sm mb-3">{{ template.description }}</p>

                                <!-- Script Preview -->
                                <div class="script-preview mb-3">
                                    <div class="preview-header flex justify-content-between align-items-center mb-2">
                                        <span class="text-sm font-semibold text-700">Script Preview:</span>
                                        <Button icon="pi pi-copy" size="small" text @click="copyToClipboard(template.content)" v-tooltip.left="'Copy to clipboard'" />
                                    </div>
                                    <div class="script-content p-3 bg-gray-50 border-round border-1 border-200">
                                        <pre class="text-sm text-700 m-0 whitespace-pre-wrap">{{ template.content }}</pre>
                                    </div>
                                </div>

                                <!-- Variables Used -->
                                <div class="variables-used">
                                    <div class="text-sm font-semibold text-700 mb-2">Variables used:</div>
                                    <div class="flex flex-wrap gap-1">
                                        <Tag v-for="variable in template.variables" :key="variable" :value="`[${variable}]`" severity="info" class="text-xs" />
                                    </div>
                                </div>
                            </div>
                        </template>
                    </Card>
                </div>
            </div>

            <!-- No Examples Message -->
            <div v-if="filteredTemplates.length === 0" class="no-examples text-center py-8">
                <i class="pi pi-search text-4xl text-400 mb-3"></i>
                <h3 class="text-xl font-semibold text-600 mb-2">No Examples Found</h3>
                <p class="text-600">
                    {{ selectedAction ? 'No examples available for the selected action.' : 'No examples available.' }}
                </p>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-content-between align-items-center">
                <div class="text-sm text-600">
                    <i class="pi pi-info-circle mr-1"></i>
                    Click "Use Template" to apply an example to your script
                </div>
                <Button label="Close" severity="secondary" @click="$emit('update:visible', false)" />
            </div>
        </template>
    </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useToast } from 'primevue/usetoast';
import { DEFAULT_SCRIPT_TEMPLATES } from '../types';
import type { ScriptTemplate } from '../types';

// Props
interface Props {
    visible: boolean;
    leadActions: Array<{ key: string; config: any }>;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    'update:visible': [value: boolean];
    'use-template': [actionKey: string, content: string];
}>();

// Composables
const toast = useToast();

// State
const selectedAction = ref<string | null>(null);

// Available templates
const availableTemplates = ref<ScriptTemplate[]>(DEFAULT_SCRIPT_TEMPLATES);

// Computed
const actionOptions = computed(() => {
    return props.leadActions.map((action) => ({
        key: action.key,
        label: getActionLabel(action.key)
    }));
});

const filteredTemplates = computed(() => {
    if (!selectedAction.value) {
        return availableTemplates.value;
    }
    return availableTemplates.value.filter((template) => template.actionKey === selectedAction.value);
});

// Methods
const getActionLabel = (actionKey: string): string => {
    const action = props.leadActions.find((a) => a.key === actionKey);
    return action?.config?.label || actionKey.charAt(0).toUpperCase() + actionKey.slice(1);
};

const getActionIcon = (actionKey: string): string => {
    const action = props.leadActions.find((a) => a.key === actionKey);
    return action?.config?.icon || 'pi pi-tag';
};

const getActionStyle = (actionKey: string) => {
    const action = props.leadActions.find((a) => a.key === actionKey);
    if (action?.config) {
        return {
            backgroundColor: action.config.bg_color || '#6366f1',
            color: action.config.text_color || '#ffffff'
        };
    }
    return {
        backgroundColor: '#6366f1',
        color: '#ffffff'
    };
};

const useTemplate = (template: ScriptTemplate) => {
    emit('use-template', template.actionKey, template.content);
    toast.add({
        severity: 'success',
        summary: 'Template Applied',
        detail: `${template.title} template has been applied`,
        life: 3000
    });
};

const copyToClipboard = async (content: string) => {
    try {
        await navigator.clipboard.writeText(content);
        toast.add({
            severity: 'success',
            summary: 'Copied',
            detail: 'Script content copied to clipboard',
            life: 2000
        });
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        toast.add({
            severity: 'error',
            summary: 'Copy Failed',
            detail: 'Failed to copy content to clipboard',
            life: 3000
        });
    }
};
</script>

<style scoped>
.script-examples {
    @apply max-h-screen overflow-y-auto;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.example-card {
    @apply h-full;
}

.example-card :deep(.p-card) {
    @apply h-full;
}

.example-card :deep(.p-card-body) {
    @apply p-0 h-full flex flex-col;
}

.example-card :deep(.p-card-content) {
    @apply flex-1 p-3;
}

.card-header {
    @apply bg-gray-50;
}

.action-badge {
    min-width: 100px;
    justify-content: center;
}

.script-content {
    @apply max-h-32 overflow-y-auto;
}

.script-content pre {
    @apply font-sans leading-relaxed;
}

.variables-used {
    @apply border-t border-surface-200 dark:border-surface-700 pt-3 mt-3;
}

.no-examples {
    @apply w-full;
}

:deep(.p-dropdown) {
    @apply rounded-lg;
}

:deep(.p-card-header) {
    @apply p-0;
}

@media (max-width: 768px) {
    .examples-grid {
        grid-template-columns: 1fr;
    }
}
</style>
