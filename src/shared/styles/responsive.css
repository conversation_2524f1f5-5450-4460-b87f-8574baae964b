/* Responsive Design System for Onboarding Features */

/* Mobile-first approach with consistent breakpoints */
:root {
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Touch-friendly sizing */
  --touch-target-min: 44px;
  --touch-spacing: 12px;
  
  /* Mobile spacing */
  --mobile-padding: 1rem;
  --mobile-margin: 0.75rem;
  --mobile-gap: 0.75rem;
}

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 100%;
  padding-left: var(--mobile-padding);
  padding-right: var(--mobile-padding);
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .responsive-container {
    max-width: 1536px;
  }
}

/* Touch-friendly button sizing */
.touch-friendly {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--touch-spacing);
}

/* Mobile navigation improvements */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--surface-border);
  padding: 1rem;
  z-index: 1000;
}

.dark .mobile-nav {
  background: var(--surface-900);
  border-top-color: var(--surface-700);
}

/* Responsive grid system */
.responsive-grid {
  display: grid;
  gap: var(--mobile-gap);
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .responsive-grid {
    gap: 1.5rem;
  }
  
  .responsive-grid.cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid.cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 2rem;
  }
  
  .responsive-grid.cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive flex utilities */
.responsive-flex {
  display: flex;
  flex-direction: column;
  gap: var(--mobile-gap);
}

@media (min-width: 768px) {
  .responsive-flex.row-md {
    flex-direction: row;
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-flex.row-lg {
    flex-direction: row;
    gap: 2rem;
  }
}

/* Mobile-specific hiding/showing */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* Responsive text sizing */
.responsive-text-sm {
  font-size: 0.875rem;
}

.responsive-text-base {
  font-size: 1rem;
}

.responsive-text-lg {
  font-size: 1.125rem;
}

.responsive-text-xl {
  font-size: 1.25rem;
}

.responsive-text-2xl {
  font-size: 1.5rem;
}

.responsive-text-3xl {
  font-size: 1.875rem;
}

@media (min-width: 768px) {
  .responsive-text-lg {
    font-size: 1.25rem;
  }
  
  .responsive-text-xl {
    font-size: 1.5rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.875rem;
  }
  
  .responsive-text-3xl {
    font-size: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .responsive-text-2xl {
    font-size: 2rem;
  }
  
  .responsive-text-3xl {
    font-size: 2.5rem;
  }
}

/* Mobile form improvements */
.mobile-form {
  width: 100%;
}

.mobile-form .p-inputtext,
.mobile-form .p-dropdown,
.mobile-form .p-textarea,
.mobile-form .p-button {
  width: 100%;
  min-height: var(--touch-target-min);
}

.mobile-form .p-button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Mobile card improvements */
.mobile-card {
  margin: var(--mobile-margin);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .mobile-card {
    margin: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
}

/* Mobile step indicators */
.mobile-steps {
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.mobile-steps::-webkit-scrollbar {
  height: 4px;
}

.mobile-steps::-webkit-scrollbar-track {
  background: var(--surface-200);
  border-radius: 2px;
}

.mobile-steps::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: 2px;
}

/* Mobile modal improvements */
@media (max-width: 767px) {
  .p-dialog {
    width: 95vw !important;
    max-width: 95vw !important;
    margin: 1rem !important;
  }
  
  .p-dialog .p-dialog-content {
    padding: 1rem !important;
  }
  
  .p-dialog .p-dialog-header {
    padding: 1rem !important;
  }
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .responsive-container {
    border: 2px solid;
  }
  
  .mobile-card {
    border: 2px solid;
  }
}

/* Focus improvements for keyboard navigation */
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .mobile-nav,
  .desktop-only,
  .mobile-only {
    display: none !important;
  }
  
  .responsive-container {
    max-width: none;
    padding: 0;
  }
}
