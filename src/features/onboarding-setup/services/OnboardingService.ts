import { ref, computed, reactive } from 'vue';
import type { 
    OnboardingState, 
    OnboardingFeature, 
    OnboardingProgress, 
    FeatureStatus,
    OnboardingOptions,
    OnboardingEvent
} from '../types';
import { 
    ONBOARDING_FEATURES, 
    FEATURE_ORDER, 
    FEATURE_DEPENDENCIES,
    DEFAULT_ONBOARDING_OPTIONS 
} from '../constants';

export class OnboardingService {
    private state = reactive<OnboardingState>({
        isActive: false,
        currentFeatureId: undefined,
        currentStepIndex: 0,
        features: [],
        progress: {
            totalFeatures: 0,
            completedFeatures: 0,
            skippedFeatures: 0,
            progressPercentage: 0,
            estimatedTimeRemaining: '0 minutes',
            startedAt: new Date(),
            lastUpdatedAt: new Date()
        },
        selectedFeatures: [],
        completedFeatures: [],
        skippedFeatures: [],
        isLoading: false
    });

    private options: OnboardingOptions = { ...DEFAULT_ONBOARDING_OPTIONS };
    private eventListeners: ((event: OnboardingEvent) => void)[] = [];

    constructor(options?: Partial<OnboardingOptions>) {
        if (options) {
            this.options = { ...this.options, ...options };
        }
        this.initializeFeatures();
    }

    // Public getters
    get currentState() {
        return this.state;
    }

    get isActive() {
        return this.state.isActive;
    }

    get currentFeature() {
        return this.state.features.find(f => f.id === this.state.currentFeatureId);
    }

    get progress() {
        return this.state.progress;
    }

    get canProceed() {
        const current = this.currentFeature;
        if (!current) return false;
        
        // Check dependencies
        const dependencies = FEATURE_DEPENDENCIES[current.id] || [];
        return dependencies.every(depId => 
            this.state.completedFeatures.includes(depId) || 
            this.state.skippedFeatures.includes(depId)
        );
    }

    get nextFeature() {
        const currentIndex = this.state.features.findIndex(f => f.id === this.state.currentFeatureId);
        if (currentIndex === -1 || currentIndex >= this.state.features.length - 1) {
            return null;
        }
        return this.state.features[currentIndex + 1];
    }

    get previousFeature() {
        const currentIndex = this.state.features.findIndex(f => f.id === this.state.currentFeatureId);
        if (currentIndex <= 0) {
            return null;
        }
        return this.state.features[currentIndex - 1];
    }

    // Initialize features based on configuration
    private initializeFeatures() {
        this.state.features = FEATURE_ORDER.map(featureId => {
            const config = ONBOARDING_FEATURES[featureId];
            return {
                id: featureId,
                name: featureId,
                title: config.title,
                description: config.description,
                icon: config.icon,
                color: config.color,
                category: config.category as any,
                priority: FEATURE_ORDER.indexOf(featureId),
                estimatedTime: config.estimatedTime,
                dependencies: FEATURE_DEPENDENCIES[featureId] || [],
                component: featureId,
                isRequired: config.isRequired,
                isCompleted: false,
                isSkipped: false
            };
        });

        this.updateProgress();
    }

    // Start onboarding process
    async startOnboarding(selectedFeatures?: string[]) {
        this.state.isActive = true;
        this.state.progress.startedAt = new Date();
        
        if (selectedFeatures && this.options.enableFeatureSelection) {
            this.state.selectedFeatures = selectedFeatures;
            // Filter features to only selected ones (but keep required features)
            this.state.features = this.state.features.filter(f => 
                f.isRequired || selectedFeatures.includes(f.id)
            );
        } else {
            this.state.selectedFeatures = this.state.features.map(f => f.id);
        }

        // Start with first feature
        if (this.state.features.length > 0) {
            this.state.currentFeatureId = this.state.features[0].id;
        }

        this.updateProgress();
        this.emitEvent({ type: 'feature_started', featureId: this.state.currentFeatureId, timestamp: new Date() });
    }

    // Complete current feature
    async completeFeature(featureId: string, data?: any) {
        const feature = this.state.features.find(f => f.id === featureId);
        if (!feature) return;

        feature.isCompleted = true;
        feature.completedAt = new Date();
        feature.data = data;

        if (!this.state.completedFeatures.includes(featureId)) {
            this.state.completedFeatures.push(featureId);
        }

        this.updateProgress();
        this.emitEvent({ 
            type: 'feature_completed', 
            featureId, 
            timestamp: new Date(), 
            data 
        });

        if (this.options.onFeatureComplete) {
            this.options.onFeatureComplete(featureId, data);
        }

        // Auto-advance if enabled
        if (this.options.autoAdvance) {
            await this.nextStep();
        }

        // Check if onboarding is complete
        if (this.isOnboardingComplete()) {
            await this.completeOnboarding();
        }
    }

    // Skip current feature
    async skipFeature(featureId: string) {
        if (!this.options.allowSkipping) return;

        const feature = this.state.features.find(f => f.id === featureId);
        if (!feature || feature.isRequired) return;

        feature.isSkipped = true;
        
        if (!this.state.skippedFeatures.includes(featureId)) {
            this.state.skippedFeatures.push(featureId);
        }

        this.updateProgress();
        this.emitEvent({ 
            type: 'feature_skipped', 
            featureId, 
            timestamp: new Date() 
        });

        if (this.options.onSkip) {
            this.options.onSkip(featureId);
        }

        // Move to next feature
        await this.nextStep();

        // Check if onboarding is complete
        if (this.isOnboardingComplete()) {
            await this.completeOnboarding();
        }
    }

    // Navigate to next step
    async nextStep() {
        const next = this.nextFeature;
        if (next) {
            this.state.currentFeatureId = next.id;
            this.state.currentStepIndex++;
            this.emitEvent({ 
                type: 'feature_started', 
                featureId: next.id, 
                timestamp: new Date() 
            });
        }
    }

    // Navigate to previous step
    async previousStep() {
        const previous = this.previousFeature;
        if (previous) {
            this.state.currentFeatureId = previous.id;
            this.state.currentStepIndex--;
        }
    }

    // Jump to specific feature
    async goToFeature(featureId: string) {
        const feature = this.state.features.find(f => f.id === featureId);
        if (!feature) return;

        // Check if we can access this feature (dependencies met)
        const dependencies = FEATURE_DEPENDENCIES[featureId] || [];
        const canAccess = dependencies.every(depId => 
            this.state.completedFeatures.includes(depId) || 
            this.state.skippedFeatures.includes(depId)
        );

        if (canAccess) {
            this.state.currentFeatureId = featureId;
            this.state.currentStepIndex = this.state.features.findIndex(f => f.id === featureId);
        }
    }

    // Check if onboarding is complete
    private isOnboardingComplete(): boolean {
        const totalFeatures = this.state.features.length;
        const processedFeatures = this.state.completedFeatures.length + this.state.skippedFeatures.length;
        return processedFeatures >= totalFeatures;
    }

    // Complete onboarding
    private async completeOnboarding() {
        this.state.isActive = false;
        this.state.currentFeatureId = undefined;
        this.updateProgress();
        
        this.emitEvent({ 
            type: 'onboarding_completed', 
            timestamp: new Date() 
        });

        if (this.options.onComplete) {
            this.options.onComplete();
        }

        if (this.options.redirectOnComplete) {
            // Handle redirect (would need router integration)
            console.log('Redirecting to:', this.options.redirectOnComplete);
        }
    }

    // Update progress calculations
    private updateProgress() {
        const total = this.state.features.length;
        const completed = this.state.completedFeatures.length;
        const skipped = this.state.skippedFeatures.length;
        
        this.state.progress = {
            ...this.state.progress,
            totalFeatures: total,
            completedFeatures: completed,
            skippedFeatures: skipped,
            progressPercentage: total > 0 ? Math.round(((completed + skipped) / total) * 100) : 0,
            estimatedTimeRemaining: this.calculateEstimatedTime(),
            lastUpdatedAt: new Date()
        };
    }

    // Calculate estimated time remaining
    private calculateEstimatedTime(): string {
        const remainingFeatures = this.state.features.filter(f => 
            !f.isCompleted && !f.isSkipped
        );
        
        let totalMinutes = 0;
        remainingFeatures.forEach(feature => {
            const timeStr = feature.estimatedTime;
            const minutes = parseInt(timeStr.match(/\d+/)?.[0] || '0');
            totalMinutes += minutes;
        });

        if (totalMinutes === 0) return '0 minutes';
        if (totalMinutes < 60) return `${totalMinutes} minutes`;
        
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }

    // Event handling
    addEventListener(listener: (event: OnboardingEvent) => void) {
        this.eventListeners.push(listener);
    }

    removeEventListener(listener: (event: OnboardingEvent) => void) {
        const index = this.eventListeners.indexOf(listener);
        if (index > -1) {
            this.eventListeners.splice(index, 1);
        }
    }

    private emitEvent(event: OnboardingEvent) {
        this.eventListeners.forEach(listener => listener(event));
    }

    // Error handling
    handleError(error: string, featureId?: string) {
        this.state.error = error;
        this.emitEvent({ 
            type: 'error', 
            featureId, 
            timestamp: new Date(), 
            error 
        });

        if (this.options.onError) {
            this.options.onError(error, featureId);
        }
    }

    // Reset onboarding
    reset() {
        this.state.isActive = false;
        this.state.currentFeatureId = undefined;
        this.state.currentStepIndex = 0;
        this.state.completedFeatures = [];
        this.state.skippedFeatures = [];
        this.state.selectedFeatures = [];
        this.state.error = undefined;
        
        // Reset feature states
        this.state.features.forEach(feature => {
            feature.isCompleted = false;
            feature.isSkipped = false;
            feature.completedAt = undefined;
            feature.data = undefined;
        });

        this.updateProgress();
    }
}
