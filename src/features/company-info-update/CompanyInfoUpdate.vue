<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useTenantStore } from '@/entities/tenant/store';
import { useAuthStore } from '@/entities/auth';
import { storeToRefs } from 'pinia';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingSteps, OnboardingCard, type OnboardingStep } from '@/shared/components/onboarding';

// PrimeVue components
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import Textarea from 'primevue/textarea';
import Button from 'primevue/button';
import ProgressSpinner from 'primevue/progressspinner';
import InputSwitch from 'primevue/inputswitch';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import { validateEmail, validateRequired, validatePhoneNumber, validateUrl } from '@/utils';
import { INDUSTRY_OPTIONS, COMPANY_SIZE_OPTIONS, COUNTRY_OPTIONS } from './types';
import { CompanyInfoService } from './services/companyInfoService';
import type { Tenant } from '@/entities/tenant/model';

// Props
const props = defineProps<{
    tenantId?: string;
    readonly?: boolean;
    showHeader?: boolean;
    showSaveButton?: boolean; // Control whether to show save buttons
}>();

// Stores
const toast = useToast();
const tenantStore = useTenantStore();
const authStore = useAuthStore();
const { isLoading } = storeToRefs(tenantStore);
const { autosaveTenant, cancelAutosave, isAutosaveInProgress, subscribeToTenantChanges, unsubscribeFromTenantChanges } = tenantStore;

// Local state
const companyInfo = ref<Partial<Tenant>>({});
const isFormValid = ref(false);
const isSaving = ref(false);
const hasUnsavedChanges = ref(false);
const originalData = ref<Partial<Tenant>>({});
const submitted = ref(false);
const currentStep = ref(0);

// Autosave state
const isAutosaving = ref(false);
const lastAutosaveTime = ref<Date | null>(null);
const autosaveEnabled = ref(true);
const tenantSubscription = ref<(() => void) | null>(null);

// Form errors - simplified for essential fields only
const errors = ref({
    name: '',
    emailAddress: '',
    phone: '',
    website: '',
    industry: '',
    description: '',
    city: '',
    country: ''
});

// Computed properties
const currentTenantId = computed(() => {
    const userData = !authStore.userData ? authStore.getUserData() : authStore.userData;
    return props.tenantId || userData?.tenantId || '';
});

const isReadonly = computed(() => {
    return props.readonly || false;
});

const showHeaderSection = computed(() => {
    return props.showHeader !== false;
});

const showSaveButtons = computed(() => {
    return props.showSaveButton !== false;
});

// Formatted steps for the new OnboardingSteps component
const formattedSteps = computed<OnboardingStep[]>(() =>
    steps.map((step, index) => ({
        id: step.id,
        title: step.title,
        description: step.description,
        icon: step.icon,
        completed: isStepCompleted(index)
    }))
);

// Validation functions
const validateField = (field: keyof typeof errors.value, value: string, validator: (val: string) => string) => {
    const error = validator(value);
    errors.value[field] = error;
    return !error;
};

const validateRequiredField = (field: keyof typeof errors.value, value: string) => {
    return validateField(field, value, validateRequired);
};

const validateEmailField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateEmail);
};

const validatePhoneField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validatePhoneNumber);
};

const validateUrlField = (field: keyof typeof errors.value, value: string) => {
    if (!value) {
        errors.value[field] = '';
        return true;
    }
    return validateField(field, value, validateUrl);
};

const validateFormFields = () => {
    const validations = [
        validateRequiredField('name', companyInfo.value.name || ''),
        validateRequiredField('emailAddress', companyInfo.value.emailAddress || ''),
        validateEmailField('emailAddress', companyInfo.value.emailAddress || ''),
        validatePhoneField('phone', companyInfo.value.phone || ''),
        validateUrlField('website', companyInfo.value.website || '')
    ];

    // Validate postal code if provided
    if (companyInfo.value.postalCode) {
        validations.push(validateField('postalCode', companyInfo.value.postalCode, validatePostalCode));
    }

    // Validate company registration number if provided
    if (companyInfo.value.companyRegistrationNumber) {
        validations.push(validateField('companyRegistrationNumber', companyInfo.value.companyRegistrationNumber, validateCompanyNumber));
    }

    // Validate VAT number if provided
    if (companyInfo.value.vatNumber) {
        validations.push(validateField('vatNumber', companyInfo.value.vatNumber, validateVatNumber));
    }

    // Validate founded year if provided
    if (companyInfo.value.foundedYear) {
        const currentYear = new Date().getFullYear();
        const yearStr = companyInfo.value.foundedYear.toString();
        const yearError = validateNumber(yearStr);
        if (yearError) {
            errors.value.foundedYear = yearError;
            validations.push(false);
        } else {
            const year = parseInt(yearStr);
            if (year < 1800 || year > currentYear) {
                errors.value.foundedYear = `Please enter a year between 1800 and ${currentYear}.`;
                validations.push(false);
            } else {
                errors.value.foundedYear = '';
                validations.push(true);
            }
        }
    }

    const isValid = validations.every(Boolean);
    isFormValid.value = isValid;
    return isValid;
};

// Methods
const loadCompanyInfo = async () => {
    if (!currentTenantId.value) {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'No tenant ID available',
            life: 3000
        });
        return;
    }

    try {
        const data = await CompanyInfoService.getCompanyInfo(currentTenantId.value);
        companyInfo.value = { ...data };
        originalData.value = { ...data };
        hasUnsavedChanges.value = false;
    } catch (error) {
        console.error('Error loading company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load company information',
            life: 3000
        });
    }
};

const saveCompanyInfo = async (data?: Partial<Tenant>) => {
    submitted.value = true;
    const dataToSave = data || companyInfo.value;

    if (!validateFormFields()) {
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Please fix the errors in the form',
            life: 3000
        });
        return;
    }

    isSaving.value = true;

    try {
        // Convert to tenant format and save using existing updateTenant method
        const tenantData = CompanyInfoService.convertToTenantData(dataToSave);

        if (!currentTenantId.value) {
            currentTenantId.value = await tenantStore.createTenant(tenantData);
        } else {
            await tenantStore.updateTenant(currentTenantId.value, tenantData);
        }
        return;
        // Update local state
        originalData.value = { ...dataToSave };
        hasUnsavedChanges.value = false;
        submitted.value = false;

        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Company information updated successfully',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving company info:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save company information',
            life: 3000
        });
    } finally {
        isSaving.value = false;
    }
};

const handleFormUpdate = () => {
    // Check if there are unsaved changes
    const hasChanges = JSON.stringify(companyInfo.value) !== JSON.stringify(originalData.value);
    hasUnsavedChanges.value = hasChanges;

    // Validate form if submitted
    if (submitted.value) {
        validateFormFields();
    }

    // Trigger autosave if enabled and there are changes
    if (autosaveEnabled.value && hasChanges && currentTenantId.value) {
        triggerAutosave();
    }
};

// Autosave functionality
const triggerAutosave = async () => {
    if (!currentTenantId.value || isAutosaving.value || !autosaveEnabled.value) {
        return;
    }

    try {
        isAutosaving.value = true;

        // Prepare data for autosave (only changed fields)
        const changedData: Partial<Tenant> = {};

        // Compare current data with original and only include changed fields
        Object.keys(companyInfo.value).forEach((key) => {
            const currentValue = companyInfo.value[key as keyof typeof companyInfo.value];
            const originalValue = originalData.value[key as keyof typeof originalData.value];

            if (currentValue !== originalValue && currentValue !== undefined && currentValue !== '') {
                (changedData as any)[key] = currentValue;
            }
        });

        // Only autosave if there are actual changes
        if (Object.keys(changedData).length > 0) {
            await autosaveTenant(currentTenantId.value, changedData, 2000);
            lastAutosaveTime.value = new Date();
        }
    } catch (error) {
        console.error('Autosave failed:', error);
        // Don't show error to user as it's background operation
    } finally {
        isAutosaving.value = false;
    }
};

// Setup real-time sync with Firestore
const setupRealtimeSync = () => {
    if (!currentTenantId.value || tenantSubscription.value) {
        return;
    }

    tenantSubscription.value = subscribeToTenantChanges(currentTenantId.value, (updatedTenant) => {
        if (updatedTenant && !isAutosaving.value) {
            // Only update if the change came from another source (not our autosave)
            const currentDataString = JSON.stringify(companyInfo.value);
            const updatedDataString = JSON.stringify(updatedTenant);

            if (currentDataString !== updatedDataString) {
                // Temporarily disable autosave to prevent loops
                autosaveEnabled.value = false;

                // Update form data with server data
                companyInfo.value = { ...updatedTenant };
                originalData.value = { ...updatedTenant };
                hasUnsavedChanges.value = false;

                // Re-enable autosave after a short delay
                setTimeout(() => {
                    autosaveEnabled.value = true;
                }, 1000);

                toast.add({
                    severity: 'info',
                    summary: 'Data Updated',
                    detail: 'Company information was updated from another session',
                    life: 3000
                });
            }
        }
    });
};

// Cleanup function
const cleanup = () => {
    if (currentTenantId.value) {
        cancelAutosave(currentTenantId.value);
    }

    if (tenantSubscription.value) {
        tenantSubscription.value();
        tenantSubscription.value = null;
    }
};

const resetForm = () => {
    companyInfo.value = { ...originalData.value };
    hasUnsavedChanges.value = false;
};

const confirmReset = () => {
    if (hasUnsavedChanges.value) {
        // Show confirmation dialog
        // For now, just reset directly
        resetForm();
        toast.add({
            severity: 'info',
            summary: 'Reset',
            detail: 'Form has been reset to original values',
            life: 3000
        });
    }
};

// Computed property to check if form has any errors
const hasErrors = computed(() => {
    return Object.values(errors.value).some((error) => error !== '');
});

// Step configuration for modern step-by-step form
const steps = [
    {
        id: 1,
        title: 'Company Basics',
        description: 'Essential company information',
        icon: 'pi pi-building',
        fields: ['name', 'industry', 'description']
    },
    {
        id: 2,
        title: 'Contact Information',
        description: 'How to reach your company',
        icon: 'pi pi-phone',
        fields: ['emailAddress', 'phone', 'website']
    },
    {
        id: 3,
        title: 'Location',
        description: 'Where your company is located',
        icon: 'pi pi-map-marker',
        fields: ['city', 'country']
    }
];

// Navigation functions
const nextStep = () => {
    if (currentStep.value < steps.length - 1) {
        currentStep.value++;
    }
};

const prevStep = () => {
    if (currentStep.value > 0) {
        currentStep.value--;
    }
};

const goToStep = (stepIndex: number) => {
    currentStep.value = stepIndex;
};

// Check if current step is valid
const isCurrentStepValid = computed(() => {
    const currentStepFields = steps[currentStep.value].fields;
    return currentStepFields.every((field) => {
        const value = companyInfo.value[field as keyof typeof companyInfo.value];
        if (field === 'name' || field === 'emailAddress') {
            return value && value.toString().trim() !== '';
        }
        return true; // Optional fields
    });
});

// Check if step has been completed
const isStepCompleted = (stepIndex: number) => {
    const stepFields = steps[stepIndex].fields;
    return stepFields.every((field) => {
        const value = companyInfo.value[field as keyof typeof companyInfo.value];
        if (field === 'name' || field === 'emailAddress') {
            return value && value.toString().trim() !== '';
        }
        return true;
    });
};

// Helper function to format autosave time
const formatAutosaveTime = (time: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

    if (diffInSeconds < 60) {
        return 'just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes}m ago`;
    } else {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours}h ago`;
    }
};

// Lifecycle
onMounted(() => {
    loadCompanyInfo();
    setupRealtimeSync();
});

onUnmounted(() => {
    cleanup();
});

// Watch for tenant ID changes
watch(currentTenantId, (newTenantId, oldTenantId) => {
    if (oldTenantId && oldTenantId !== newTenantId) {
        cleanup();
    }
    if (newTenantId) {
        setupRealtimeSync();
    }
});

// Expose methods for parent components
defineExpose({
    loadCompanyInfo,
    saveCompanyInfo,
    resetForm,
    hasUnsavedChanges: computed(() => hasUnsavedChanges.value),
    isFormValid: computed(() => isFormValid.value)
});
</script>

<template>
    <OnboardingLayout v-if="!isLoading" maxWidth="xl">
        <!-- Header -->
        <template v-if="showHeaderSection" #header>
            <OnboardingHeader title="Company Information" description="Let's set up your company profile in a few simple steps" icon="pi pi-building">
                <!-- Autosave Toggle -->
                <template #actions>
                    <div class="flex items-center gap-2">
                        <label for="autosave-toggle" class="text-sm text-surface-600 dark:text-surface-400"> Auto-save </label>
                        <InputSwitch id="autosave-toggle" v-model="autosaveEnabled" :disabled="isReadonly" />
                    </div>
                </template>
            </OnboardingHeader>
        </template>

        <!-- Steps -->
        <template #steps>
            <OnboardingSteps :steps="formattedSteps" :current-step="currentStep" :allow-step-navigation="true" @step-click="goToStep" />
        </template>

        <!-- Main Content -->
        <OnboardingCard :title="steps[currentStep].title" :subtitle="steps[currentStep].description" :icon="steps[currentStep].icon" variant="default" size="large">
            <!-- Step Content -->
            <!-- Step 1: Company Basics -->
            <div v-if="currentStep === 0" class="space-y-6">
                <!-- Company Name -->
                <div class="field">
                    <label for="name" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Company Name <span class="text-red-500">*</span> </label>
                    <IconField>
                        <InputIcon class="pi pi-building" />
                        <InputText id="name" v-model.trim="companyInfo.name" :class="{ 'p-invalid': errors.name && submitted }" :readonly="isReadonly" placeholder="Enter your company name" @input="handleFormUpdate" fluid size="large" />
                    </IconField>
                    <small class="p-error" v-if="errors.name && submitted">{{ errors.name }}</small>
                </div>

                <!-- Industry -->
                <div class="field">
                    <label for="industry" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Industry </label>
                    <IconField>
                        <Dropdown
                            id="industry"
                            v-model="companyInfo.industry"
                            :options="INDUSTRY_OPTIONS"
                            optionLabel="label"
                            optionValue="value"
                            :readonly="isReadonly"
                            placeholder="Select your industry"
                            @change="handleFormUpdate"
                            fluid
                            size="large"
                        />
                    </IconField>
                </div>

                <!-- Company Description -->
                <div class="field">
                    <label for="description" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Company Description </label>
                    <Textarea id="description" v-model="companyInfo.description" :readonly="isReadonly" placeholder="Tell us about your company..." rows="4" @input="handleFormUpdate" fluid class="resize-none" />
                    <small class="text-surface-500 dark:text-surface-400 text-xs mt-1"> Optional: Brief description of what your company does </small>
                </div>
            </div>

            <!-- Step 2: Contact Information -->
            <div v-if="currentStep === 1" class="space-y-6">
                <!-- Email Address -->
                <div class="field">
                    <label for="email" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Primary Email <span class="text-red-500">*</span> </label>
                    <IconField>
                        <InputIcon class="pi pi-envelope" />
                        <InputText
                            id="email"
                            v-model.trim="companyInfo.emailAddress"
                            :class="{ 'p-invalid': errors.emailAddress && submitted }"
                            :readonly="isReadonly"
                            placeholder="<EMAIL>"
                            type="email"
                            @input="handleFormUpdate"
                            fluid
                            size="large"
                        />
                    </IconField>
                    <small class="p-error" v-if="errors.emailAddress && submitted">{{ errors.emailAddress }}</small>
                </div>

                <!-- Phone Number -->
                <div class="field">
                    <label for="phone" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Phone Number </label>
                    <IconField>
                        <InputIcon class="pi pi-phone" />
                        <InputText id="phone" v-model.trim="companyInfo.phone" :class="{ 'p-invalid': errors.phone && submitted }" :readonly="isReadonly" placeholder="+****************" @input="handleFormUpdate" fluid size="large" />
                    </IconField>
                    <small class="p-error" v-if="errors.phone && submitted">{{ errors.phone }}</small>
                </div>

                <!-- Website -->
                <div class="field">
                    <label for="website" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Website </label>
                    <IconField>
                        <InputIcon class="pi pi-globe" />
                        <InputText
                            id="website"
                            v-model.trim="companyInfo.website"
                            :class="{ 'p-invalid': errors.website && submitted }"
                            :readonly="isReadonly"
                            placeholder="https://www.example.com"
                            type="url"
                            @input="handleFormUpdate"
                            fluid
                            size="large"
                        />
                    </IconField>
                    <small class="p-error" v-if="errors.website && submitted">{{ errors.website }}</small>
                    <small class="text-surface-500 dark:text-surface-400 text-xs mt-1"> Optional: Your company website URL </small>
                </div>
            </div>

            <!-- Step 3: Location -->
            <div v-if="currentStep === 2" class="space-y-6">
                <!-- City -->
                <div class="field">
                    <label for="city" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> City </label>
                    <IconField>
                        <InputIcon class="pi pi-map-marker" />
                        <InputText id="city" v-model.trim="companyInfo.city" :readonly="isReadonly" placeholder="Enter your city" @input="handleFormUpdate" fluid size="large" />
                    </IconField>
                </div>

                <!-- Country -->
                <div class="field">
                    <label for="country" class="block font-semibold mb-3 text-surface-900 dark:text-surface-0 text-lg"> Country </label>
                    <IconField>
                        <Dropdown id="country" v-model="companyInfo.country" :options="COUNTRY_OPTIONS" optionLabel="label" optionValue="value" :readonly="isReadonly" placeholder="Select your country" @change="handleFormUpdate" fluid size="large" />
                    </IconField>
                </div>

                <!-- Completion Message -->
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mt-6">
                    <div class="flex items-center gap-3">
                        <i class="pi pi-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                        <div>
                            <h3 class="font-semibold text-green-800 dark:text-green-200">Almost Done!</h3>
                            <p class="text-green-700 dark:text-green-300 text-sm">Review your information and save your company profile.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Footer -->
            <template #footer>
                <div class="flex justify-between items-center">
                    <!-- Previous Button -->
                    <Button v-if="currentStep > 0" label="Previous" icon="pi pi-chevron-left" severity="secondary" outlined @click="prevStep" :disabled="isSaving" />
                    <div v-else></div>

                    <!-- Step Counter and Autosave Status -->
                    <div class="flex flex-col items-center gap-3 mx-10">
                        <div class="text-sm text-surface-600 dark:text-surface-400">Step {{ currentStep + 1 }} of {{ steps.length }}</div>

                        <!-- Autosave Status -->
                        <div class="flex items-center gap-2 text-xs">
                            <div v-if="isAutosaving" class="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                                <i class="pi pi-spin pi-spinner"></i>
                                <span>Saving...</span>
                            </div>
                            <div v-else-if="lastAutosaveTime" class="flex items-center gap-1 text-green-600 dark:text-green-400">
                                <i class="pi pi-check"></i>
                                <span>Saved {{ formatAutosaveTime(lastAutosaveTime) }}</span>
                            </div>
                            <div v-else-if="hasUnsavedChanges" class="flex items-center gap-1 text-orange-600 dark:text-orange-400">
                                <i class="pi pi-clock"></i>
                                <span>Unsaved changes</span>
                            </div>
                        </div>
                    </div>

                    <!-- Next/Save Button -->
                    <div class="flex gap-2 items-center">
                        <!-- Unsaved Changes Indicator -->
                        <div v-if="hasUnsavedChanges && !isReadonly" class="flex items-center gap-2 mr-4">
                            <i class="pi pi-exclamation-triangle text-orange-500 text-sm"></i>
                            <span class="text-orange-600 dark:text-orange-400 text-sm">Unsaved changes</span>
                        </div>

                        <!-- Reset Button -->
                        <Button v-if="hasUnsavedChanges && !isReadonly" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" size="small" />

                        <!-- Next/Save Button -->
                        <Button v-if="currentStep < steps.length - 1" label="Next" icon="pi pi-chevron-right" iconPos="right" @click="nextStep" :disabled="!isCurrentStepValid || isSaving" />
                        <Button v-else label="Save Company Info" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
                    </div>
                </div>
            </template>
        </OnboardingCard>

        <!-- Footer Actions (for widget mode) -->
        <template v-if="!showHeaderSection && !isReadonly && showSaveButtons" #footer>
            <OnboardingCard variant="outlined" size="small">
                <div class="flex justify-end gap-2">
                    <Button v-if="hasUnsavedChanges" label="Reset" icon="pi pi-refresh" severity="secondary" outlined @click="confirmReset" :disabled="isSaving" />
                    <Button label="Save Changes" icon="pi pi-save" severity="success" @click="saveCompanyInfo(companyInfo)" :loading="isSaving" :disabled="!isFormValid || !hasUnsavedChanges" />
                </div>
            </OnboardingCard>
        </template>
    </OnboardingLayout>

    <!-- Loading State -->
    <div v-else class="flex justify-center items-center py-8">
        <ProgressSpinner />
    </div>
</template>

<style scoped>
/* Custom styles for company info specific elements */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.field {
    margin-bottom: 1.5rem;
}

.field label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: block;
}

/* Enhanced form styling */
:deep(.p-inputtext),
:deep(.p-dropdown),
:deep(.p-textarea) {
    border-radius: 0.75rem;
    border: 2px solid var(--surface-border);
    transition: all 0.2s ease;
}

:deep(.p-inputtext:focus),
:deep(.p-dropdown:focus),
:deep(.p-textarea:focus) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

:deep(.p-inputtext.p-invalid),
:deep(.p-dropdown.p-invalid),
:deep(.p-textarea.p-invalid) {
    border-color: var(--red-500);
}

/* Button enhancements */
:deep(.p-button:hover) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .flex.gap-2 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .flex.gap-2 .p-button {
        width: 100%;
    }
}
</style>
