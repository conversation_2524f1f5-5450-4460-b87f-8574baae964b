<template>
    <Dialog :visible="visible" @update:visible="$emit('update:visible', $event)" modal header="AI Scripts Usage Guide" :style="{ width: '60rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
        <div class="usage-info">
            <!-- Introduction -->
            <div class="intro-section mb-5">
                <div class="flex align-items-center gap-3 mb-3">
                    <div class="icon-wrapper p-3 bg-primary-100 border-round">
                        <i class="pi pi-lightbulb text-primary text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-900 mb-1">What are AI Scripts?</h2>
                        <p class="text-600">AI scripts are personalized message templates that help automate your customer communications.</p>
                    </div>
                </div>

                <div class="benefits-grid grid">
                    <div class="col-12 md:col-4">
                        <div class="benefit-card text-center p-3">
                            <i class="pi pi-clock text-3xl text-blue-500 mb-2"></i>
                            <h4 class="font-semibold mb-1">Save Time</h4>
                            <p class="text-sm text-600">Automate repetitive communications</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="benefit-card text-center p-3">
                            <i class="pi pi-users text-3xl text-green-500 mb-2"></i>
                            <h4 class="font-semibold mb-1">Personalize</h4>
                            <p class="text-sm text-600">Use variables for personal touch</p>
                        </div>
                    </div>
                    <div class="col-12 md:col-4">
                        <div class="benefit-card text-center p-3">
                            <i class="pi pi-chart-line text-3xl text-purple-500 mb-2"></i>
                            <h4 class="font-semibold mb-1">Improve Results</h4>
                            <p class="text-sm text-600">Consistent, professional messaging</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- How It Works -->
            <div class="how-it-works mb-5">
                <h3 class="text-lg font-semibold mb-3 flex align-items-center gap-2">
                    <i class="pi pi-cog text-primary"></i>
                    How It Works
                </h3>

                <div class="steps">
                    <div class="step flex gap-3 mb-4">
                        <div class="step-number">
                            <div class="w-2rem h-2rem bg-primary text-white border-round flex align-items-center justify-content-center font-semibold">1</div>
                        </div>
                        <div class="step-content">
                            <h4 class="font-semibold mb-1">Create Scripts</h4>
                            <p class="text-600 text-sm">Write personalized scripts for each lead action (inquired, quoted, booked, etc.)</p>
                        </div>
                    </div>

                    <div class="step flex gap-3 mb-4">
                        <div class="step-number">
                            <div class="w-2rem h-2rem bg-primary text-white border-round flex align-items-center justify-content-center font-semibold">2</div>
                        </div>
                        <div class="step-content">
                            <h4 class="font-semibold mb-1">Add Variables</h4>
                            <p class="text-600 text-sm">Use variables like [customer_name] and [company_name] for personalization</p>
                        </div>
                    </div>

                    <div class="step flex gap-3 mb-4">
                        <div class="step-number">
                            <div class="w-2rem h-2rem bg-primary text-white border-round flex align-items-center justify-content-center font-semibold">3</div>
                        </div>
                        <div class="step-content">
                            <h4 class="font-semibold mb-1">AI Takes Over</h4>
                            <p class="text-600 text-sm">AI automatically uses your scripts when communicating with leads</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Best Practices -->
            <div class="best-practices mb-5">
                <h3 class="text-lg font-semibold mb-3 flex align-items-center gap-2">
                    <i class="pi pi-star text-yellow-500"></i>
                    Best Practices
                </h3>

                <div class="practices-list">
                    <div class="practice-item flex align-items-start gap-3 mb-3">
                        <i class="pi pi-check-circle text-green-500 mt-1"></i>
                        <div>
                            <h4 class="font-semibold mb-1">Keep it conversational</h4>
                            <p class="text-600 text-sm">Write as if you're speaking directly to the customer</p>
                        </div>
                    </div>

                    <div class="practice-item flex align-items-start gap-3 mb-3">
                        <i class="pi pi-check-circle text-green-500 mt-1"></i>
                        <div>
                            <h4 class="font-semibold mb-1">Use personalization</h4>
                            <p class="text-600 text-sm">Include customer name and relevant details using variables</p>
                        </div>
                    </div>

                    <div class="practice-item flex align-items-start gap-3 mb-3">
                        <i class="pi pi-check-circle text-green-500 mt-1"></i>
                        <div>
                            <h4 class="font-semibold mb-1">Be clear and concise</h4>
                            <p class="text-600 text-sm">Get to the point while maintaining a friendly tone</p>
                        </div>
                    </div>

                    <div class="practice-item flex align-items-start gap-3 mb-3">
                        <i class="pi pi-check-circle text-green-500 mt-1"></i>
                        <div>
                            <h4 class="font-semibold mb-1">Include a call-to-action</h4>
                            <p class="text-600 text-sm">Tell the customer what you want them to do next</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Variable Examples -->
            <div class="variable-examples mb-5">
                <h3 class="text-lg font-semibold mb-3 flex align-items-center gap-2">
                    <i class="pi pi-code text-blue-500"></i>
                    Variable Examples
                </h3>

                <div class="examples-grid grid">
                    <div class="col-12 md:col-6">
                        <div class="example-card p-3 border-1 border-200 border-round">
                            <h4 class="font-semibold mb-2 text-sm">Before (Generic)</h4>
                            <div class="code-block p-2 bg-gray-100 border-round text-sm">"Hello, thank you for your inquiry about our services."</div>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="example-card p-3 border-1 border-200 border-round">
                            <h4 class="font-semibold mb-2 text-sm">After (Personalized)</h4>
                            <div class="code-block p-2 bg-blue-50 border-round text-sm">"Hello [customer_name], thank you for your inquiry about our [service_type] services."</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Common Variables -->
            <div class="common-variables">
                <h3 class="text-lg font-semibold mb-3 flex align-items-center gap-2">
                    <i class="pi pi-list text-purple-500"></i>
                    Most Used Variables
                </h3>

                <div class="variables-grid grid">
                    <div class="col-12 md:col-6">
                        <div class="variable-item flex justify-content-between align-items-center p-2 border-round hover:bg-gray-50">
                            <div>
                                <code class="text-primary font-semibold">[customer_name]</code>
                                <p class="text-xs text-600 mt-1">Customer's name</p>
                            </div>
                            <i class="pi pi-user text-gray-400"></i>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="variable-item flex justify-content-between align-items-center p-2 border-round hover:bg-gray-50">
                            <div>
                                <code class="text-primary font-semibold">[agent_name]</code>
                                <p class="text-xs text-600 mt-1">Your agent name</p>
                            </div>
                            <i class="pi pi-users text-gray-400"></i>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="variable-item flex justify-content-between align-items-center p-2 border-round hover:bg-gray-50">
                            <div>
                                <code class="text-primary font-semibold">[company_name]</code>
                                <p class="text-xs text-600 mt-1">Your company name</p>
                            </div>
                            <i class="pi pi-building text-gray-400"></i>
                        </div>
                    </div>
                    <div class="col-12 md:col-6">
                        <div class="variable-item flex justify-content-between align-items-center p-2 border-round hover:bg-gray-50">
                            <div>
                                <code class="text-primary font-semibold">[service_type]</code>
                                <p class="text-xs text-600 mt-1">Type of service</p>
                            </div>
                            <i class="pi pi-wrench text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-content-end">
                <Button label="Got it!" @click="$emit('update:visible', false)" />
            </div>
        </template>
    </Dialog>
</template>

<script setup lang="ts">
// Props
interface Props {
    visible: boolean;
}

defineProps<Props>();

// Emits
defineEmits<{
    'update:visible': [value: boolean];
}>();
</script>

<style scoped lang="scss">
.usage-info {
    // @apply max-h-80vh overflow-y-auto;
    @apply overflow-y-auto;
}

.benefit-card {
    @apply border border-surface-200 dark:border-surface-700 rounded-lg transition-all duration-200;
}

.benefit-card:hover {
    @apply shadow-lg border-primary-200;
}

.step {
    @apply relative;
}

.step:not(:last-child)::after {
    content: '';
    @apply absolute left-4 top-8 w-px h-6 bg-gray-200;
}

.practice-item {
    @apply transition-all duration-200 p-2 rounded-lg;
}

.practice-item:hover {
    @apply bg-gray-50;
}

.code-block {
    @apply font-mono;
}

.variable-item {
    @apply transition-all duration-200;
}

.variables-grid .variable-item code {
    @apply text-xs px-2 py-1 bg-primary-50 rounded-lg;
}

:deep(.p-dialog-content) {
    @apply p-0;
}

:deep(.p-dialog-header) {
    @apply rounded-t-lg;
}
</style>
