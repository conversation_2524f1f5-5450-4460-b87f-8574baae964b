/**
 * Calendar Onboarding Feature
 *
 * This feature provides a comprehensive prompts-onboarding flow for Google Calendar integration.
 * It allows users to create a Google Calendar, validates the integration, and provides
 * real-time status updates through Firestore subscriptions.
 */

// Main component
export { default as OnboardingCalendar } from './OnboardingCalendar.vue';

// Components
export { default as CalendarNameInput } from './components/CalendarNameInput.vue';
export { default as CalendarStatusDisplay } from './components/CalendarStatusDisplay.vue';
export { default as OnboardingSteps } from './components/OnboardingSteps.vue';

// Service
export { useCalendarOnboardingService, CalendarOnboardingService } from './services/calendarOnboardingService';

// Types
export type { CalendarConfig, OnboardingStep, CalendarCreationForm, CalendarOnboardingState, CalendarCreationResponse } from './types';

export { CALENDAR_ONBOARDING_STEPS, DEFAULT_CALENDAR_FORM, VALIDATION_RULES, ERROR_MESSAGES, SUCCESS_MESSAGES } from './types';
