import {
    DEFAULT_SCRIPT_VARIABLES,
    SCRIPT_VALIDATION_RULES
} from '../types';
import type {
    ScriptValidationResult,
    ScriptVariable
} from '../types';

/**
 * Script Validation Service
 * Handles validation of AI script content, variables, and formatting
 */
export class ScriptValidationService {
    private availableVariables: ScriptVariable[];
    private validationRules: typeof SCRIPT_VALIDATION_RULES;

    constructor(
        availableVariables: ScriptVariable[] = DEFAULT_SCRIPT_VARIABLES,
        validationRules: typeof SCRIPT_VALIDATION_RULES = SCRIPT_VALIDATION_RULES
    ) {
        this.availableVariables = availableVariables;
        this.validationRules = validationRules;
    }

    /**
     * Validate script content
     */
    validateScript(content: string, actionKey?: string): ScriptValidationResult {
        const errors: string[] = [];
        const warnings: string[] = [];
        const variables = this.extractVariables(content);

        // Check minimum length
        if (content.trim().length < this.validationRules.minLength) {
            errors.push(`Script must be at least ${this.validationRules.minLength} characters long`);
        }

        // Check maximum length
        if (content.length > this.validationRules.maxLength) {
            errors.push(`Script must not exceed ${this.validationRules.maxLength} characters`);
        }

        // Check for required variables
        const missingRequired = this.validationRules.requiredVariables.filter(
            required => !variables.includes(required)
        );
        if (missingRequired.length > 0) {
            errors.push(`Missing required variables: ${missingRequired.join(', ')}`);
        }

        // Check for invalid variables
        const invalidVariables = this.findInvalidVariables(content);
        if (invalidVariables.length > 0) {
            errors.push(`Invalid variables found: ${invalidVariables.join(', ')}`);
        }

        // Check for forbidden patterns
        for (const pattern of this.validationRules.forbiddenPatterns) {
            const matches = content.match(pattern);
            if (matches) {
                errors.push(`Invalid variable format found: ${matches.join(', ')}`);
            }
        }

        // Action-specific validations
        if (actionKey) {
            const actionWarnings = this.validateActionSpecific(content, actionKey, variables);
            warnings.push(...actionWarnings);
        }

        // General warnings
        if (variables.length === 0) {
            warnings.push('No variables found in script. Consider adding personalization variables.');
        }

        if (content.includes('[') && content.includes(']') && variables.length === 0) {
            warnings.push('Found bracket notation but no valid variables. Check variable formatting.');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            variables
        };
    }

    /**
     * Extract variables from script content
     */
    extractVariables(content: string): string[] {
        const variablePattern = /\[(\w+)\]/g;
        const matches = content.match(variablePattern);
        
        if (!matches) return [];

        const variables = matches.map(match => match.slice(1, -1)); // Remove brackets
        const validVariables = variables.filter(variable => 
            this.availableVariables.some(av => av.key === variable)
        );

        return [...new Set(validVariables)]; // Remove duplicates
    }

    /**
     * Find invalid variables in content
     */
    findInvalidVariables(content: string): string[] {
        const variablePattern = /\[(\w+)\]/g;
        const matches = content.match(variablePattern);
        
        if (!matches) return [];

        const variables = matches.map(match => match.slice(1, -1));
        const validVariableKeys = this.availableVariables.map(v => v.key);
        
        const invalidVariables = variables.filter(variable => 
            !validVariableKeys.includes(variable)
        );

        return [...new Set(invalidVariables)]; // Remove duplicates
    }

    /**
     * Action-specific validation warnings
     */
    private validateActionSpecific(content: string, actionKey: string, variables: string[]): string[] {
        const warnings: string[] = [];

        switch (actionKey) {
            case 'quoted':
                if (!variables.includes('quote_amount')) {
                    warnings.push('Consider including [quote_amount] for quote follow-ups');
                }
                break;

            case 'booked':
                if (!variables.includes('appointment_date')) {
                    warnings.push('Consider including [appointment_date] for appointment confirmations');
                }
                break;

            case 'converted':
                if (!variables.includes('service_type')) {
                    warnings.push('Consider including [service_type] for conversion confirmations');
                }
                break;

            case 'inquired':
                if (!variables.includes('conversation_summary')) {
                    warnings.push('Consider including [conversation_summary] for inquiry responses');
                }
                break;
        }

        return warnings;
    }

    /**
     * Get suggestions for improving script
     */
    getSuggestions(content: string, actionKey?: string): string[] {
        const suggestions: string[] = [];
        const variables = this.extractVariables(content);

        // Suggest common variables if missing
        if (!variables.includes('customer_name')) {
            suggestions.push('Add [customer_name] to personalize the message');
        }

        if (!variables.includes('company_name')) {
            suggestions.push('Add [company_name] to reinforce your brand');
        }

        // Action-specific suggestions
        if (actionKey) {
            const actionSuggestions = this.getActionSpecificSuggestions(content, actionKey, variables);
            suggestions.push(...actionSuggestions);
        }

        return suggestions;
    }

    /**
     * Get action-specific suggestions
     */
    private getActionSpecificSuggestions(content: string, actionKey: string, variables: string[]): string[] {
        const suggestions: string[] = [];

        switch (actionKey) {
            case 'quoted':
                if (!content.toLowerCase().includes('quote') && !content.toLowerCase().includes('estimate')) {
                    suggestions.push('Consider mentioning the quote or estimate in your message');
                }
                break;

            case 'booked':
                if (!content.toLowerCase().includes('appointment') && !content.toLowerCase().includes('meeting')) {
                    suggestions.push('Consider confirming the appointment details');
                }
                break;

            case 'converted':
                if (!content.toLowerCase().includes('thank') && !content.toLowerCase().includes('welcome')) {
                    suggestions.push('Consider thanking the customer or welcoming them');
                }
                break;
        }

        return suggestions;
    }

    /**
     * Format script content with proper variable syntax
     */
    formatScript(content: string): string {
        // Fix common variable formatting issues
        let formatted = content;

        // Fix variables without brackets
        this.availableVariables.forEach(variable => {
            const regex = new RegExp(`\\b${variable.key}\\b(?!\\])`, 'gi');
            formatted = formatted.replace(regex, `[${variable.key}]`);
        });

        // Fix double brackets
        formatted = formatted.replace(/\[\[(\w+)\]\]/g, '[$1]');

        // Fix spaces in brackets
        formatted = formatted.replace(/\[\s*(\w+)\s*\]/g, '[$1]');

        return formatted;
    }

    /**
     * Get available variables for a specific category
     */
    getVariablesByCategory(category: ScriptVariable['category']): ScriptVariable[] {
        return this.availableVariables.filter(variable => variable.category === category);
    }

    /**
     * Get variable by key
     */
    getVariable(key: string): ScriptVariable | undefined {
        return this.availableVariables.find(variable => variable.key === key);
    }

    /**
     * Add custom variable
     */
    addCustomVariable(variable: ScriptVariable): void {
        const existingIndex = this.availableVariables.findIndex(v => v.key === variable.key);
        if (existingIndex >= 0) {
            this.availableVariables[existingIndex] = variable;
        } else {
            this.availableVariables.push(variable);
        }
    }

    /**
     * Remove custom variable
     */
    removeCustomVariable(key: string): void {
        this.availableVariables = this.availableVariables.filter(v => v.key !== key);
    }

    /**
     * Update validation rules
     */
    updateValidationRules(rules: Partial<typeof SCRIPT_VALIDATION_RULES>): void {
        this.validationRules = { ...this.validationRules, ...rules };
    }
}

/**
 * Composable for using script validation service
 */
export function useScriptValidation(
    availableVariables?: ScriptVariable[],
    validationRules?: typeof SCRIPT_VALIDATION_RULES
) {
    const validationService = new ScriptValidationService(availableVariables, validationRules);

    return {
        validationService,
        
        // Convenience methods
        validateScript: (content: string, actionKey?: string) => 
            validationService.validateScript(content, actionKey),
            
        extractVariables: (content: string) => 
            validationService.extractVariables(content),
            
        getSuggestions: (content: string, actionKey?: string) => 
            validationService.getSuggestions(content, actionKey),
            
        formatScript: (content: string) => 
            validationService.formatScript(content),
            
        getVariablesByCategory: (category: ScriptVariable['category']) => 
            validationService.getVariablesByCategory(category),
            
        getVariable: (key: string) => 
            validationService.getVariable(key),
            
        addCustomVariable: (variable: ScriptVariable) => 
            validationService.addCustomVariable(variable),
            
        removeCustomVariable: (key: string) => 
            validationService.removeCustomVariable(key)
    };
}
