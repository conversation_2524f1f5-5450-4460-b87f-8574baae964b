import { ref, computed } from 'vue';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant';
import { requestCreateGoogleCalendar } from '@/shared/api/appointment';
import type { 
    CalendarOnboardingState, 
    CalendarCreationForm, 
    OnboardingStep,
    CalendarCreationResponse 
} from '../types';
import { 
    CALENDAR_ONBOARDING_STEPS, 
    DEFAULT_CALENDAR_FORM, 
    VALIDATION_RULES, 
    ERROR_MESSAGES,
    SUCCESS_MESSAGES 
} from '../types';
import type { Tenant } from '@/entities/tenant/model';

/**
 * Service class for calendar onboarding functionality
 */
export class CalendarOnboardingService {
    private authStore = useAuthStore();
    private tenantStore = useTenantStore();
    private tenantUnsubscribe: (() => void) | null = null;

    // State
    private state = ref<CalendarOnboardingState>({
        currentStep: 0,
        steps: [...CALENDAR_ONBOARDING_STEPS],
        formData: { ...DEFAULT_CALENDAR_FORM },
        isLoading: false,
        error: null,
        calendarId: null,
        isValidated: false,
        tenantId: null
    });

    // Computed properties
    get currentStep() {
        return computed(() => this.state.value.steps[this.state.value.currentStep]);
    }

    get isFormValid() {
        return computed(() => this.validateCalendarName(this.state.value.formData.calendarName).isValid);
    }

    get canProceed() {
        return computed(() => {
            if (this.state.value.currentStep === 0) {
                return this.isFormValid.value && !this.state.value.isLoading;
            }
            return this.state.value.isValidated;
        });
    }

    get getState() {
        return computed(() => this.state.value);
    }

    /**
     * Validates calendar name according to rules
     */
    static validateCalendarName(name: string) {
        const rules = VALIDATION_RULES.calendarName;
        
        if (!name || name.trim().length === 0) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_REQUIRED };
        }
        
        if (name.length < rules.minLength) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_TOO_SHORT };
        }
        
        if (name.length > rules.maxLength) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_TOO_LONG };
        }
        
        if (!rules.pattern.test(name)) {
            return { isValid: false, error: ERROR_MESSAGES.CALENDAR_NAME_INVALID };
        }
        
        return { isValid: true, error: undefined };
    }

    /**
     * Checks if onboarding is complete
     */
    static isOnboardingComplete(tenant: Tenant): boolean {
        const calendar = tenant.calendar;
        return !!(calendar?.calendarId && calendar?.validated);
    }

    /**
     * Gets the current onboarding step based on tenant data
     */
    static getCurrentStep(tenant: Tenant): number {
        const calendar = tenant.calendar;

        if (!calendar?.calendarId) {
            return 0; // Setup step
        }

        if (!calendar?.validated) {
            return 1; // Validation step
        }

        return 2; // Complete step
    }

    /**
     * Initialize the onboarding service
     */
    async initialize(): Promise<void> {
        try {
            this.setLoading(true);
            this.setError(null);

            // Get user data and tenant ID
            const userData = await this.authStore.getUserData();
            if (!userData?.tenantId) {
                throw new Error(ERROR_MESSAGES.TENANT_NOT_FOUND);
            }

            this.state.value.tenantId = userData.tenantId;

            // Get tenant data to check existing calendar
            const tenant = await this.tenantStore.getTenant(userData.tenantId);
            if (tenant?.calendar) {
                this.state.value.calendarId = tenant.calendar.calendarId || null;
                this.state.value.isValidated = tenant.calendar.validated || false;
                
                // Set current step based on tenant state
                const currentStep = CalendarOnboardingService.getCurrentStep(tenant);
                this.state.value.currentStep = currentStep;
                
                // Update step statuses
                this.updateStepsBasedOnProgress(currentStep);
            }

            // Subscribe to tenant changes for real-time updates
            this.subscribeToTenantChanges(userData.tenantId);

        } catch (error) {
            console.error('Error initializing calendar onboarding:', error);
            this.setError(error instanceof Error ? error.message : 'Failed to initialize onboarding');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Update form data
     */
    updateFormData(updates: Partial<CalendarCreationForm>): void {
        this.state.value.formData = { ...this.state.value.formData, ...updates };
        
        // Validate calendar name if it's being updated
        if (updates.calendarName !== undefined) {
            const validation = CalendarOnboardingService.validateCalendarName(updates.calendarName);
            this.state.value.formData.isValid = validation.isValid;
            this.state.value.formData.error = validation.error;
        }
    }

    /**
     * Set error state
     */
    setError(error: string | null): void {
        this.state.value.error = error;
    }

    /**
     * Set loading state
     */
    setLoading(loading: boolean): void {
        this.state.value.isLoading = loading;
    }

    /**
     * Update step status
     */
    private updateStepStatus(stepIndex: number, completed: boolean, active: boolean = false): void {
        if (stepIndex >= 0 && stepIndex < this.state.value.steps.length) {
            this.state.value.steps[stepIndex].completed = completed;
            this.state.value.steps[stepIndex].active = active;
        }
    }

    /**
     * Update steps based on current progress
     */
    private updateStepsBasedOnProgress(currentStep: number): void {
        this.state.value.steps.forEach((step, index) => {
            if (index < currentStep) {
                this.updateStepStatus(index, true, false);
            } else if (index === currentStep) {
                this.updateStepStatus(index, false, true);
            } else {
                this.updateStepStatus(index, false, false);
            }
        });
    }

    /**
     * Move to next step
     */
    private nextStep(): void {
        if (this.state.value.currentStep < this.state.value.steps.length - 1) {
            // Mark current step as completed
            this.updateStepStatus(this.state.value.currentStep, true, false);
            
            // Move to next step
            this.state.value.currentStep++;
            this.updateStepStatus(this.state.value.currentStep, false, true);
        }
    }

    /**
     * Subscribe to tenant changes for real-time updates
     */
    private subscribeToTenantChanges(tenantId: string): void {
        // Clean up existing subscription
        if (this.tenantUnsubscribe) {
            this.tenantUnsubscribe();
        }

        this.tenantUnsubscribe = this.tenantStore.subscribeToTenantChanges(tenantId, (tenant: Tenant | null) => {
            if (tenant?.calendar) {
                const previousCalendarId = this.state.value.calendarId;
                const previousValidated = this.state.value.isValidated;

                this.state.value.calendarId = tenant.calendar.calendarId || null;
                this.state.value.isValidated = tenant.calendar.validated || false;

                // If calendar was just created (new calendar ID)
                if (tenant.calendar.calendarId && !previousCalendarId) {
                    this.nextStep(); // Move to validation step
                }

                // If calendar was just validated
                if (tenant.calendar.validated && !previousValidated) {
                    this.nextStep(); // Move to complete step
                }
            }
        });
    }

    /**
     * Create Google Calendar
     */
    async createCalendar(): Promise<CalendarCreationResponse> {
        try {
            this.setLoading(true);
            this.setError(null);

            if (!this.isFormValid.value) {
                throw new Error(this.state.value.formData.error || 'Form validation failed');
            }

            if (!this.state.value.tenantId) {
                throw new Error(ERROR_MESSAGES.TENANT_NOT_FOUND);
            }

            const calendarId = await requestCreateGoogleCalendar(
                this.state.value.formData.calendarName,
                this.state.value.formData.timezone
            );

            if (calendarId) {
                this.state.value.calendarId = calendarId;
                return { id: calendarId, success: true };
            } else {
                throw new Error('No calendar ID returned from server');
            }

        } catch (error) {
            console.error('Error creating calendar:', error);
            const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.CREATION_FAILED;
            this.setError(errorMessage);
            return { id: '', success: false, error: errorMessage };
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Reset onboarding state
     */
    reset(): void {
        this.state.value = {
            currentStep: 0,
            steps: [...CALENDAR_ONBOARDING_STEPS],
            formData: { ...DEFAULT_CALENDAR_FORM },
            isLoading: false,
            error: null,
            calendarId: null,
            isValidated: false,
            tenantId: null
        };

        // Clean up subscription
        this.cleanup();
    }

    /**
     * Clean up subscriptions
     */
    cleanup(): void {
        if (this.tenantUnsubscribe) {
            this.tenantUnsubscribe();
            this.tenantUnsubscribe = null;
        }
    }

    /**
     * Validate calendar name (instance method)
     */
    validateCalendarName(name: string) {
        return CalendarOnboardingService.validateCalendarName(name);
    }
}

/**
 * Composable function to use the calendar onboarding service
 */
export function useCalendarOnboardingService() {
    const service = new CalendarOnboardingService();

    return {
        // State
        state: service.getState,
        currentStep: service.currentStep,
        isFormValid: service.isFormValid,
        canProceed: service.canProceed,

        // Methods
        initialize: () => service.initialize(),
        updateFormData: (updates: Partial<CalendarCreationForm>) => service.updateFormData(updates),
        setError: (error: string | null) => service.setError(error),
        setLoading: (loading: boolean) => service.setLoading(loading),
        createCalendar: () => service.createCalendar(),
        reset: () => service.reset(),
        cleanup: () => service.cleanup(),
        validateCalendarName: (name: string) => service.validateCalendarName(name)
    };
}
