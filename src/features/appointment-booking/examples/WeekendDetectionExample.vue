<template>
    <div class="weekend-detection-example">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="pi pi-calendar mr-2"></i>
                    Weekend Detection Example
                </h3>
                <p class="card-subtitle">Demonstrates how weekends are detected using London timezone</p>
            </div>

            <div class="card-content">
                <div class="grid">
                    <div class="col-12 md:col-6">
                        <div class="example-section">
                            <h4>Current Information</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Your Local Time:</label>
                                    <span>{{ localTime }}</span>
                                </div>
                                <div class="info-item">
                                    <label>London Time:</label>
                                    <span>{{ londonTime }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Your Local Timezone:</label>
                                    <span>{{ userTimezone }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Appointment Timezone:</label>
                                    <span>{{ appointmentTimezone }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="example-section">
                            <h4>Weekend Detection</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Today (Local):</label>
                                    <span :class="{ weekend: isLocalWeekend }">
                                        {{ localDateString }}
                                        <i v-if="isLocalWeekend" class="pi pi-ban text-red-500"></i>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <label>Today (London):</label>
                                    <span :class="{ weekend: isLondonWeekend }">
                                        {{ londonDateString }}
                                        <i v-if="isLondonWeekend" class="pi pi-ban text-red-500"></i>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <label>Calendar Uses:</label>
                                    <span class="font-semibold text-blue-600"> London Weekend Detection </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="example-section">
                        <h4>Test Different Dates</h4>
                        <div class="flex gap-3 flex-wrap">
                            <Button
                                v-for="testDate in testDates"
                                :key="testDate.label"
                                :label="testDate.label"
                                :class="{
                                    'p-button-danger': testDate.isWeekendInLondon,
                                    'p-button-success': !testDate.isWeekendInLondon
                                }"
                                size="small"
                                @click="selectedTestDate = testDate"
                            />
                        </div>

                        <div v-if="selectedTestDate" class="mt-3 p-3 border-round surface-border border-1">
                            <h5>{{ selectedTestDate.label }} Analysis</h5>
                            <div class="grid">
                                <div class="col-12 md:col-6">
                                    <div class="text-sm">
                                        <strong>Date:</strong> {{ selectedTestDate.date.toDateString() }}<br />
                                        <strong>Day in London:</strong> {{ selectedTestDate.dayInLondon }}<br />
                                        <strong>Weekend in London:</strong>
                                        <span :class="selectedTestDate.isWeekendInLondon ? 'text-red-500' : 'text-green-500'">
                                            {{ selectedTestDate.isWeekendInLondon ? 'Yes' : 'No' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-12 md:col-6">
                                    <div class="text-sm">
                                        <strong>Calendar Status:</strong>
                                        <span :class="selectedTestDate.isWeekendInLondon ? 'text-red-500' : 'text-green-500'">
                                            {{ selectedTestDate.isWeekendInLondon ? 'Disabled' : 'Available' }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { isDateWeekendInLondon, getTodayInLondon } from '../utils/timezone';
import { getAppointmentTimezone, getUserTimezone } from '../config/timezone';

const localTime = ref<string>('');
const londonTime = ref<string>('');
const selectedTestDate = ref<any>(null);

const userTimezone = computed(() => getUserTimezone());
const appointmentTimezone = computed(() => getAppointmentTimezone());

const updateTimes = () => {
    const now = new Date();

    localTime.value = now.toLocaleTimeString();
    londonTime.value = now.toLocaleTimeString('en-GB', {
        timeZone: appointmentTimezone.value
    });
};

const today = new Date();
const todayInLondon = getTodayInLondon();

const localDateString = computed(() => today.toDateString());
const londonDateString = computed(() => todayInLondon.toDateString());

const isLocalWeekend = computed(() => {
    const day = today.getDay();
    return day === 0 || day === 6;
});

const isLondonWeekend = computed(() => isDateWeekendInLondon(today));

// Test dates for demonstration
const testDates = computed(() => {
    const dates = [];
    const baseDate = new Date();

    for (let i = -3; i <= 10; i++) {
        const testDate = new Date(baseDate);
        testDate.setDate(baseDate.getDate() + i);

        const londonDay = testDate.toLocaleDateString('en-US', {
            weekday: 'long',
            timeZone: appointmentTimezone.value
        });

        dates.push({
            label: `${testDate.getDate()}/${testDate.getMonth() + 1}`,
            date: testDate,
            dayInLondon: londonDay,
            isWeekendInLondon: isDateWeekendInLondon(testDate)
        });
    }

    return dates;
});

let timeInterval: NodeJS.Timeout | null = null;

onMounted(() => {
    updateTimes();
    timeInterval = setInterval(updateTimes, 1000);
});

onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval);
    }
});
</script>

<style scoped>
.weekend-detection-example {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
}

.card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--surface-border);
    background: var(--surface-ground);
}

.card-title {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-title i {
    color: var(--primary-color);
}

.card-subtitle {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.card-content {
    padding: 1.5rem;
}

.example-section {
    background: var(--surface-ground);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--surface-border);
}

.example-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--surface-card);
    border-radius: 4px;
    border: 1px solid var(--surface-border);
}

.info-item label {
    font-weight: 500;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
}

.info-item span {
    font-weight: 600;
    color: var(--text-color);
}

.weekend {
    color: var(--red-500) !important;
}
</style>
