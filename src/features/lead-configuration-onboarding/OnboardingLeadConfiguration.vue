<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useToast } from 'primevue/usetoast';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingCard, FeatureIcon } from '@/shared/components/onboarding';

// Feature components and services
import { LeadConfigService } from './services/leadConfigService';
import ConfigurationManager from './components/ConfigurationManager.vue';
import type { LeadConfiguration, ConfigurationType } from './types';
import { CONFIGURATION_SECTIONS } from './types';

// Props
interface Props {
    visible?: boolean;
    showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    visible: true,
    showHeader: true
});

// Emits
const emit = defineEmits<{
    complete: [];
    close: [];
}>();

// Reactive state
const loading = ref(false);
const configuration = ref<LeadConfiguration | null>(null);
const activeTab = ref<ConfigurationType>('lead_actions');
const hasUnsavedChanges = ref(false);
const toast = useToast();

// Computed
const currentSection = computed(() => {
    console.log('======');
    console.log(CONFIGURATION_SECTIONS);
    return CONFIGURATION_SECTIONS.find((section) => section.key === activeTab.value);
});

const currentConfig = computed(() => {
    if (!configuration.value || !activeTab.value) return {};
    return configuration.value[activeTab.value] || {};
});

// Methods
const loadConfiguration = async () => {
    loading.value = true;
    try {
        const config = await LeadConfigService.getTenantConfiguration();
        if (config) {
            configuration.value = config;
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to load configuration',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error loading configuration:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load configuration',
            life: 3000
        });
    } finally {
        loading.value = false;
    }
};

const handleConfigurationUpdate = async (configurationType: ConfigurationType, updatedConfig: any) => {
    try {
        const success = await LeadConfigService.updateTenantConfiguration(configurationType, updatedConfig);

        if (success) {
            // Update local configuration
            if (configuration.value) {
                configuration.value[configurationType] = updatedConfig;
            }

            hasUnsavedChanges.value = false;
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: `${currentSection.value?.title} updated successfully`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to update configuration',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error updating configuration:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update configuration',
            life: 3000
        });
    }
};

const handleTabChange = (newTab: ConfigurationType) => {
    if (hasUnsavedChanges.value) {
        // Could add confirmation dialog here
        console.warn('Switching tabs with unsaved changes');
    }
    activeTab.value = newTab;
    hasUnsavedChanges.value = false;
};

const handleComplete = () => {
    if (hasUnsavedChanges.value) {
        toast.add({
            severity: 'warn',
            summary: 'Warning',
            detail: 'Please save your changes before completing',
            life: 3000
        });
        return;
    }

    emit('complete');
};

const handleClose = () => {
    if (hasUnsavedChanges.value) {
        // Could add confirmation dialog here
        console.warn('Closing with unsaved changes');
    }
    emit('close');
};

const resetToDefaults = async () => {
    if (!activeTab.value) return;

    try {
        const success = await LeadConfigService.resetToDefaults(activeTab.value);
        if (success) {
            await loadConfiguration();
            toast.add({
                severity: 'success',
                summary: 'Success',
                detail: `${currentSection.value?.title} reset to defaults`,
                life: 3000
            });
        } else {
            toast.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to reset to defaults',
                life: 3000
            });
        }
    } catch (error) {
        console.error('Error resetting to defaults:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to reset to defaults',
            life: 3000
        });
    }
};

// Lifecycle
onMounted(async () => {
    await loadConfiguration();

    // Subscribe to real-time updates
    LeadConfigService.subscribeToTenantChanges((config) => {
        if (config && !hasUnsavedChanges.value) {
            configuration.value = config;
        }
    });
});

onUnmounted(() => {
    LeadConfigService.unsubscribeFromTenantChanges();
});
</script>

<template>
    <OnboardingLayout v-if="!loading" maxWidth="2xl">
        <!-- Header -->
        <template v-if="showHeader" #header>
            <OnboardingHeader title="Lead Configuration" description="Customize your lead actions, sources, and statuses to match your business workflow" icon="pi pi-cog">
                <template #actions>
                    <div class="flex gap-2">
                        <Button label="Reset to Defaults" icon="pi pi-refresh" severity="secondary" outlined @click="resetToDefaults" :disabled="loading" />
                        <Button label="Complete" icon="pi pi-check" @click="handleComplete" :disabled="loading || hasUnsavedChanges" />
                    </div>
                </template>
            </OnboardingHeader>
        </template>

        <!-- Configuration Content -->
        <div v-if="configuration" class="space-y-6">
            <!-- Tab Navigation -->
            <OnboardingCard variant="outlined" size="small">
                <div class="flex flex-wrap gap-2">
                    <Button
                        v-for="section in CONFIGURATION_SECTIONS"
                        :key="section.key"
                        :label="section.title"
                        :icon="section.icon"
                        @click="handleTabChange(section.key)"
                        :severity="activeTab === section.key ? 'primary' : 'secondary'"
                        :outlined="activeTab !== section.key"
                        size="small"
                        class="flex-1 min-w-fit"
                    />
                </div>
            </OnboardingCard>

            <!-- Tab Content -->
            <OnboardingCard v-if="currentSection" :title="currentSection.title" :subtitle="currentSection.description" :icon="currentSection.icon" variant="default" size="large">
                <!-- Configuration Manager -->
                <ConfigurationManager v-if="activeTab && currentConfig" :configuration-type="activeTab" :initial-config="currentConfig" @update="handleConfigurationUpdate" @changes="hasUnsavedChanges = $event" />
            </OnboardingCard>
        </div>

        <!-- Error State -->
        <OnboardingCard v-else variant="error" size="medium" centered>
            <div class="text-center space-y-4">
                <FeatureIcon icon="pi pi-exclamation-triangle" size="large" variant="error" />
                <div>
                    <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">Failed to Load Configuration</h3>
                    <p class="text-surface-600 dark:text-surface-400 mb-4">There was an error loading your lead configuration.</p>
                    <Button label="Retry" icon="pi pi-refresh" @click="loadConfiguration" />
                </div>
            </div>
        </OnboardingCard>
    </OnboardingLayout>

    <!-- Loading State -->
    <div v-else class="flex justify-center items-center py-8">
        <ProgressSpinner />
    </div>
</template>

<style scoped>
/* Custom styles for lead configuration specific elements */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Responsive button adjustments */
@media (max-width: 768px) {
    .flex.gap-2 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .flex.gap-2 .p-button {
        width: 100%;
    }

    .flex.flex-wrap.gap-2 {
        flex-direction: column;
    }

    .flex.flex-wrap.gap-2 .p-button {
        flex: none;
        width: 100%;
    }
}
</style>
