<template>
    <div class="component-mapping-test">
        <Card>
            <template #header>
                <div class="p-4">
                    <h2 class="text-xl font-bold">Component Mapping Verification</h2>
                    <p class="text-surface-600 dark:text-surface-400">
                        This shows how COMPONENT_MAP is used to dynamically load feature components
                    </p>
                </div>
            </template>
            <template #content>
                <!-- Component Mapping Table -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Component Mappings from COMPONENT_MAP</h3>
                    <DataTable :value="componentMappings" class="mb-4">
                        <Column field="featureId" header="Feature ID" />
                        <Column field="componentName" header="Component Name" />
                        <Column field="isLoaded" header="Component Loaded">
                            <template #body="{ data }">
                                <Tag 
                                    :value="data.isLoaded ? 'Yes' : 'No'" 
                                    :severity="data.isLoaded ? 'success' : 'danger'"
                                />
                            </template>
                        </Column>
                        <Column field="featureTitle" header="Feature Title" />
                    </DataTable>
                </div>

                <!-- Feature Component Test -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Test Feature Component Loading</h3>
                    <div class="flex gap-3 mb-4">
                        <Dropdown
                            v-model="selectedFeatureId"
                            :options="availableFeatures"
                            option-label="title"
                            option-value="id"
                            placeholder="Select a feature to test"
                            class="w-full md:w-14rem"
                        />
                        <Button
                            label="Load Component"
                            icon="pi pi-play"
                            @click="loadSelectedComponent"
                            :disabled="!selectedFeatureId"
                        />
                    </div>

                    <!-- Dynamic Component Display -->
                    <div v-if="currentTestComponent" class="test-component-container">
                        <Card>
                            <template #header>
                                <div class="p-3 bg-primary-50 dark:bg-primary-900">
                                    <div class="flex items-center gap-2">
                                        <i class="pi pi-cog text-primary"></i>
                                        <span class="font-semibold">
                                            Testing: {{ selectedFeatureTitle }}
                                        </span>
                                    </div>
                                </div>
                            </template>
                            <template #content>
                                <div class="component-wrapper">
                                    <component
                                        :is="currentTestComponent"
                                        v-bind="getTestProps(selectedFeatureId)"
                                        @complete="handleTestComplete"
                                        @error="handleTestError"
                                    />
                                </div>
                            </template>
                        </Card>
                    </div>
                </div>

                <!-- Integration Service Info -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Integration Service Status</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                            <template #content>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">
                                        {{ totalMappings }}
                                    </div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Total Mappings
                                    </div>
                                </div>
                            </template>
                        </Card>
                        <Card>
                            <template #content>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-500">
                                        {{ loadedComponents }}
                                    </div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Loaded Components
                                    </div>
                                </div>
                            </template>
                        </Card>
                        <Card>
                            <template #content>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-500">
                                        {{ failedComponents }}
                                    </div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Failed Components
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </div>
                </div>

                <!-- Event Log -->
                <div v-if="eventLog.length > 0">
                    <h3 class="text-lg font-semibold mb-3">Component Test Log</h3>
                    <div class="space-y-2 max-h-40 overflow-y-auto">
                        <div
                            v-for="(event, index) in eventLog"
                            :key="index"
                            class="p-3 bg-surface-50 dark:bg-surface-800 rounded border-l-4"
                            :class="event.type === 'success' ? 'border-green-400' : 'border-red-400'"
                        >
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="font-medium">{{ event.message }}</div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        Feature: {{ event.featureId }}
                                    </div>
                                </div>
                                <div class="text-xs text-surface-500">
                                    {{ formatTime(event.timestamp) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';

// PrimeVue Components
import Card from 'primevue/card';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Tag from 'primevue/tag';
import Dropdown from 'primevue/dropdown';
import Button from 'primevue/button';

// Onboarding Services and Constants
import { FeatureIntegrationService } from '../services/FeatureIntegrationService';
import { COMPONENT_MAP, ONBOARDING_FEATURES } from '../constants';

// Composables
const toast = useToast();

// Services
const integrationService = new FeatureIntegrationService();

// State
const selectedFeatureId = ref<string>('');
const currentTestComponent = ref<any>(null);
const eventLog = ref<Array<{
    type: 'success' | 'error';
    message: string;
    featureId: string;
    timestamp: Date;
}>>([]);

// Computed
const componentMappings = computed(() => {
    return Object.entries(COMPONENT_MAP).map(([featureId, componentName]) => {
        const component = integrationService.getFeatureComponent(featureId);
        const feature = ONBOARDING_FEATURES[featureId];
        
        return {
            featureId,
            componentName,
            isLoaded: !!component,
            featureTitle: feature?.title || 'Unknown'
        };
    });
});

const availableFeatures = computed(() => {
    return Object.entries(ONBOARDING_FEATURES).map(([id, feature]) => ({
        id,
        title: feature.title
    }));
});

const selectedFeatureTitle = computed(() => {
    const feature = ONBOARDING_FEATURES[selectedFeatureId.value];
    return feature?.title || '';
});

const totalMappings = computed(() => Object.keys(COMPONENT_MAP).length);
const loadedComponents = computed(() => componentMappings.value.filter(m => m.isLoaded).length);
const failedComponents = computed(() => componentMappings.value.filter(m => !m.isLoaded).length);

// Methods
const loadSelectedComponent = () => {
    if (!selectedFeatureId.value) return;
    
    const component = integrationService.getFeatureComponent(selectedFeatureId.value);
    
    if (component) {
        currentTestComponent.value = component;
        addEvent('success', `Component loaded successfully`, selectedFeatureId.value);
        
        toast.add({
            severity: 'success',
            summary: 'Component Loaded',
            detail: `${selectedFeatureTitle.value} component loaded successfully`,
            life: 3000
        });
    } else {
        addEvent('error', `Failed to load component`, selectedFeatureId.value);
        
        toast.add({
            severity: 'error',
            summary: 'Component Load Failed',
            detail: `Could not load component for ${selectedFeatureTitle.value}`,
            life: 3000
        });
    }
};

const getTestProps = (featureId: string) => {
    // Return test props for the component
    const testProps = integrationService.getFeatureProps(featureId);
    return {
        ...testProps,
        // Add test mode flag
        testMode: true,
        embedded: true
    };
};

const handleTestComplete = (data: any) => {
    addEvent('success', 'Component test completed successfully', selectedFeatureId.value);
    
    toast.add({
        severity: 'success',
        summary: 'Test Complete',
        detail: `${selectedFeatureTitle.value} test completed`,
        life: 3000
    });
};

const handleTestError = (error: string) => {
    addEvent('error', `Component test error: ${error}`, selectedFeatureId.value);
    
    toast.add({
        severity: 'error',
        summary: 'Test Error',
        detail: error,
        life: 3000
    });
};

const addEvent = (type: 'success' | 'error', message: string, featureId: string) => {
    eventLog.value.unshift({
        type,
        message,
        featureId,
        timestamp: new Date()
    });
    
    // Keep only last 10 events
    if (eventLog.value.length > 10) {
        eventLog.value = eventLog.value.slice(0, 10);
    }
};

const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// Lifecycle
onMounted(() => {
    console.log('Component Mapping Test - COMPONENT_MAP:', COMPONENT_MAP);
    console.log('Component Mapping Test - Integration Service:', integrationService.getAllComponents());
});
</script>

<style scoped>
.component-mapping-test {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.test-component-container {
    margin-top: 1rem;
}

.component-wrapper {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--surface-border);
    border-radius: 0.5rem;
    padding: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .component-mapping-test {
        padding: 1rem 0.5rem;
    }
    
    .flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem;
    }
}
</style>
