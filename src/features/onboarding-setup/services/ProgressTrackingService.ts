import { reactive } from 'vue';
import type { OnboardingProgress, FeatureStatus, OnboardingFeature } from '../types';

export class ProgressTrackingService {
    private featureStatuses = reactive<Record<string, FeatureStatus>>({});
    private progress = reactive<OnboardingProgress>({
        totalFeatures: 0,
        completedFeatures: 0,
        skippedFeatures: 0,
        progressPercentage: 0,
        estimatedTimeRemaining: '0 minutes',
        startedAt: new Date(),
        lastUpdatedAt: new Date()
    });

    // Initialize progress tracking
    initialize(features: OnboardingFeature[]) {
        this.progress.totalFeatures = features.length;
        this.progress.startedAt = new Date();
        
        features.forEach(feature => {
            this.featureStatuses[feature.id] = {
                id: feature.id,
                status: 'not_started',
                progress: 0,
                lastUpdated: new Date()
            };
        });
        
        this.updateProgress();
    }

    // Update feature status
    updateFeatureStatus(featureId: string, status: FeatureStatus['status'], progress = 0, data?: any, error?: string) {
        if (!this.featureStatuses[featureId]) return;

        this.featureStatuses[featureId] = {
            ...this.featureStatuses[featureId],
            status,
            progress: Math.max(0, Math.min(100, progress)),
            lastUpdated: new Date(),
            data,
            error
        };

        this.updateProgress();
    }

    // Mark feature as completed
    completeFeature(featureId: string, data?: any) {
        this.updateFeatureStatus(featureId, 'completed', 100, data);
    }

    // Mark feature as skipped
    skipFeature(featureId: string) {
        this.updateFeatureStatus(featureId, 'skipped', 100);
    }

    // Get feature status
    getFeatureStatus(featureId: string): FeatureStatus | null {
        return this.featureStatuses[featureId] || null;
    }

    // Get current progress
    getProgress(): OnboardingProgress {
        return { ...this.progress };
    }

    // Calculate and update overall progress
    private updateProgress() {
        const statuses = Object.values(this.featureStatuses);
        const completed = statuses.filter(s => s.status === 'completed').length;
        const skipped = statuses.filter(s => s.status === 'skipped').length;
        const total = statuses.length;

        this.progress.completedFeatures = completed;
        this.progress.skippedFeatures = skipped;
        this.progress.progressPercentage = total > 0 ? Math.round(((completed + skipped) / total) * 100) : 0;
        this.progress.lastUpdatedAt = new Date();
        this.progress.estimatedTimeRemaining = this.calculateEstimatedTime();
    }

    // Calculate estimated time remaining
    private calculateEstimatedTime(): string {
        const remainingStatuses = Object.values(this.featureStatuses).filter(s => 
            s.status === 'not_started' || s.status === 'in_progress'
        );

        const estimatedMinutes = remainingStatuses.length * 5;
        
        if (estimatedMinutes === 0) return '0 minutes';
        if (estimatedMinutes < 60) return `${estimatedMinutes} minutes`;
        
        const hours = Math.floor(estimatedMinutes / 60);
        const minutes = estimatedMinutes % 60;
        return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }

    // Check if onboarding is complete
    isComplete(): boolean {
        const statuses = Object.values(this.featureStatuses);
        return statuses.every(s => s.status === 'completed' || s.status === 'skipped');
    }

    // Reset progress tracking
    reset() {
        Object.keys(this.featureStatuses).forEach(featureId => {
            this.featureStatuses[featureId] = {
                id: featureId,
                status: 'not_started',
                progress: 0,
                lastUpdated: new Date()
            };
        });

        this.progress.completedFeatures = 0;
        this.progress.skippedFeatures = 0;
        this.progress.progressPercentage = 0;
        this.progress.startedAt = new Date();
        this.progress.lastUpdatedAt = new Date();
    }
}
