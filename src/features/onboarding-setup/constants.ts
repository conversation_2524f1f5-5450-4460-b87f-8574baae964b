import type { FeatureConfig, FeatureCategory, OnboardingOptions } from './types';

export const FEATURE_CATEGORIES: Record<string, FeatureCategory> = {
    essential: {
        id: 'essential',
        name: 'essential',
        title: 'Essential Setup',
        description: 'Core business information and basic configuration',
        icon: 'pi pi-star',
        color: 'text-orange-500',
        order: 1
    },
    communication: {
        id: 'communication',
        name: 'communication',
        title: 'Communication',
        description: 'Phone, SMS, email, and calendar integration',
        icon: 'pi pi-phone',
        color: 'text-blue-500',
        order: 2
    },
    automation: {
        id: 'automation',
        name: 'automation',
        title: 'AI & Automation',
        description: 'AI prompts, scripts, and automated workflows',
        icon: 'pi pi-sparkles',
        color: 'text-purple-500',
        order: 3
    },
    configuration: {
        id: 'configuration',
        name: 'configuration',
        title: 'Advanced Configuration',
        description: 'Lead management and advanced settings',
        icon: 'pi pi-cog',
        color: 'text-green-500',
        order: 4
    }
};

export const ONBOARDING_FEATURES: Record<string, FeatureConfig> = {
    companyInfo: {
        title: 'Company Information',
        description: 'Set up your company profile, contact details, and business information',
        icon: 'pi pi-building',
        color: 'text-orange-500',
        category: 'essential',
        estimatedTime: '5 minutes',
        isRequired: true
    },
    userInfo: {
        title: 'User Profile',
        description: 'Configure your personal information and account preferences',
        icon: 'pi pi-user',
        color: 'text-orange-500',
        category: 'essential',
        estimatedTime: '3 minutes',
        isRequired: true
    },
    leadConfig: {
        title: 'Lead Configuration',
        description: 'Customize lead actions, sources, and statuses to match your business workflow',
        icon: 'pi pi-cog',
        color: 'text-green-500',
        category: 'configuration',
        estimatedTime: '8 minutes',
        isRequired: false
    },
    calendar: {
        title: 'Google Calendar Integration',
        description: 'Connect your Google Calendar to enable seamless appointment booking and management',
        icon: 'pi pi-google',
        color: 'text-blue-500',
        category: 'communication',
        estimatedTime: '5 minutes',
        isRequired: true
    },
    numbers: {
        title: 'Communication Services',
        description: 'Configure SMS and call integration using Twilio and Bland.AI services',
        icon: 'pi pi-phone',
        color: 'text-blue-500',
        category: 'communication',
        estimatedTime: '10 minutes',
        isRequired: true
    },
    email: {
        title: 'Email Integration',
        description: 'Set up email forwarding for your chatbot to handle email inquiries automatically',
        icon: 'pi pi-envelope',
        color: 'text-blue-500',
        category: 'communication',
        estimatedTime: '7 minutes',
        isRequired: true
    },
    prompts: {
        title: 'AI Prompt Configuration',
        description: 'Configure how your AI assistants interact with customers and analyze leads',
        icon: 'pi pi-sparkles',
        color: 'text-purple-500',
        category: 'automation',
        estimatedTime: '15 minutes',
        isRequired: true
    },
    scripts: {
        title: 'AI Scripts Setup',
        description: 'Create personalized scripts for your AI to use when communicating with leads',
        icon: 'pi pi-file-edit',
        color: 'text-purple-500',
        category: 'automation',
        estimatedTime: '12 minutes',
        isRequired: true
    }
};

// Optimal order for onboarding features
export const FEATURE_ORDER = [
    'scripts', // Automation: AI scripts
    'companyInfo', // Essential: Company setup first
    'userInfo', // Essential: User profile
    'leadConfig', // Configuration: Lead management
    'calendar', // Communication: Calendar for appointments
    'numbers', // Communication: Phone/SMS setup
    'email', // Communication: Email integration
    'prompts' // Automation: AI prompts
];

export const DEFAULT_ONBOARDING_OPTIONS: OnboardingOptions = {
    autoAdvance: false,
    allowSkipping: true,
    showProgress: true,
    showEstimatedTime: true,
    enableFeatureSelection: true
};

// Maps feature IDs to their corresponding Vue component names
// This is used by FeatureIntegrationService to dynamically load components
export const COMPONENT_MAP: Record<string, string> = {
    companyInfo: 'CompanyInfoUpdate', // Company information setup
    userInfo: 'CompanyUserUpdate', // User profile configuration
    leadConfig: 'OnboardingLeadConfiguration', // Lead management configuration
    calendar: 'OnboardingCalendar', // Google Calendar integration
    numbers: 'OnboardingNumber', // Phone/SMS setup (Twilio/Bland.AI)
    email: 'OnboardingEmail', // Email integration setup
    prompts: 'OnboardingPrompts', // AI prompt configuration
    scripts: 'OnboardingScripts' // AI scripts setup
};

export const FEATURE_DEPENDENCIES: Record<string, string[]> = {
    scripts: ['prompts'], // Scripts depend on prompts being configured
    leadConfig: ['companyInfo'], // Lead config depends on company info
    email: ['companyInfo'], // Email setup needs company info
    numbers: ['companyInfo'] // Phone setup needs company info
};
