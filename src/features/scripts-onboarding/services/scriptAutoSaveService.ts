import { ref } from 'vue';
import { useTenantStore } from '@/entities/tenant/store/tenantStore';
import type { Tenant } from '@/entities/tenant/model';
import type { 
    ScriptConfig, 
    ScriptsCollection, 
    ScriptAutoSaveConfig, 
    ScriptAutoSaveStatus 
} from '../types';

// Default auto-save configuration
const DEFAULT_CONFIG: Omit<ScriptAutoSaveConfig, 'tenantId'> = {
    enabled: true,
    debounceMs: 1000, // 1 second debounce
    maxRetries: 3,
    retryDelayMs: 2000
};

/**
 * Script Auto-Save Service
 * Handles automatic saving of AI scripts with debouncing and retry logic
 * Integrates with tenant store onSnapshot for real-time updates
 */
export class ScriptAutoSaveService {
    private tenantStore = useTenantStore();
    private config: ScriptAutoSaveConfig;
    private saveTimeouts = new Map<string, NodeJS.Timeout>();
    private retryCounters = new Map<string, number>();
    private statusCallbacks = new Map<string, (status: ScriptAutoSaveStatus) => void>();

    constructor(tenantId: string, config: Partial<ScriptAutoSaveConfig> = {}) {
        this.config = { 
            ...DEFAULT_CONFIG, 
            tenantId,
            ...config 
        };
    }

    /**
     * Register a status callback for a specific script
     */
    onStatusChange(scriptKey: string, callback: (status: ScriptAutoSaveStatus) => void) {
        this.statusCallbacks.set(scriptKey, callback);
    }

    /**
     * Remove status callback
     */
    offStatusChange(scriptKey: string) {
        this.statusCallbacks.delete(scriptKey);
    }

    /**
     * Update status and notify callback
     */
    private updateStatus(scriptKey: string, status: ScriptAutoSaveStatus) {
        const callback = this.statusCallbacks.get(scriptKey);
        if (callback) {
            callback(status);
        }
    }

    /**
     * Auto-save a single script
     */
    autoSaveScript(scriptKey: string, scriptConfig: ScriptConfig) {
        if (!this.config.enabled) return;

        this.debouncedSave(scriptKey, async () => {
            // Get current tenant data
            const currentTenant = await this.tenantStore.getTenant(this.config.tenantId);
            if (!currentTenant) {
                throw new Error('Tenant not found');
            }

            // Update scripts collection
            const currentScripts = currentTenant.scripts || {};
            const updatedScripts: ScriptsCollection = {
                ...currentScripts,
                [scriptKey]: {
                    ...scriptConfig,
                    lastUpdated: new Date()
                }
            };

            // Update tenant with new scripts
            const updateData: Partial<Tenant> = {
                scripts: updatedScripts
            };

            await this.tenantStore.updateTenant(this.config.tenantId, updateData);
        });
    }

    /**
     * Auto-save multiple scripts at once
     */
    autoSaveScripts(scripts: ScriptsCollection) {
        if (!this.config.enabled) return;

        this.debouncedSave('bulk-scripts', async () => {
            // Add timestamps to all scripts
            const timestampedScripts: ScriptsCollection = {};
            Object.entries(scripts).forEach(([key, script]) => {
                timestampedScripts[key] = {
                    ...script,
                    lastUpdated: new Date()
                };
            });

            const updateData: Partial<Tenant> = {
                scripts: timestampedScripts
            };

            await this.tenantStore.updateTenant(this.config.tenantId, updateData);
        });
    }

    /**
     * Delete a script
     */
    async deleteScript(scriptKey: string) {
        try {
            this.updateStatus(scriptKey, 'saving');

            const currentTenant = await this.tenantStore.getTenant(this.config.tenantId);
            if (!currentTenant) {
                throw new Error('Tenant not found');
            }

            const currentScripts = currentTenant.scripts || {};
            const { [scriptKey]: deletedScript, ...remainingScripts } = currentScripts;

            const updateData: Partial<Tenant> = {
                scripts: remainingScripts
            };

            await this.tenantStore.updateTenant(this.config.tenantId, updateData);
            this.updateStatus(scriptKey, 'saved');

            // Clean up callbacks and timeouts for deleted script
            this.cancel(scriptKey);

        } catch (error) {
            console.error(`Error deleting script ${scriptKey}:`, error);
            this.updateStatus(scriptKey, 'error');
            throw error;
        }
    }

    /**
     * Debounced save with retry logic
     */
    private debouncedSave(scriptKey: string, saveFunction: () => Promise<void>) {
        // Clear existing timeout
        const existingTimeout = this.saveTimeouts.get(scriptKey);
        if (existingTimeout) {
            clearTimeout(existingTimeout);
        }

        // Set new timeout
        const timeout = setTimeout(async () => {
            await this.executeSave(scriptKey, saveFunction);
        }, this.config.debounceMs);

        this.saveTimeouts.set(scriptKey, timeout);
    }

    /**
     * Execute save with retry logic
     */
    private async executeSave(scriptKey: string, saveFunction: () => Promise<void>) {
        this.updateStatus(scriptKey, 'saving');

        try {
            await saveFunction();
            this.updateStatus(scriptKey, 'saved');
            this.retryCounters.delete(scriptKey);

            // Reset to idle after a short delay
            setTimeout(() => {
                this.updateStatus(scriptKey, 'idle');
            }, 2000);

        } catch (error) {
            console.error(`Auto-save failed for script ${scriptKey}:`, error);
            
            const retryCount = this.retryCounters.get(scriptKey) || 0;
            
            if (retryCount < this.config.maxRetries) {
                this.retryCounters.set(scriptKey, retryCount + 1);
                
                // Retry after delay
                setTimeout(() => {
                    this.executeSave(scriptKey, saveFunction);
                }, this.config.retryDelayMs);
                
            } else {
                this.updateStatus(scriptKey, 'error');
                this.retryCounters.delete(scriptKey);
            }
        }
    }

    /**
     * Cancel all pending saves
     */
    cancelAll() {
        this.saveTimeouts.forEach(timeout => clearTimeout(timeout));
        this.saveTimeouts.clear();
        this.retryCounters.clear();
    }

    /**
     * Cancel save for specific script
     */
    cancel(scriptKey: string) {
        const timeout = this.saveTimeouts.get(scriptKey);
        if (timeout) {
            clearTimeout(timeout);
            this.saveTimeouts.delete(scriptKey);
        }
        this.retryCounters.delete(scriptKey);
        this.updateStatus(scriptKey, 'idle');
    }

    /**
     * Force immediate save (bypass debounce)
     */
    async forceSave(scriptKey: string, scriptConfig: ScriptConfig) {
        this.cancel(scriptKey);
        await this.executeSave(scriptKey, async () => {
            const currentTenant = await this.tenantStore.getTenant(this.config.tenantId);
            if (!currentTenant) {
                throw new Error('Tenant not found');
            }

            const currentScripts = currentTenant.scripts || {};
            const updatedScripts: ScriptsCollection = {
                ...currentScripts,
                [scriptKey]: {
                    ...scriptConfig,
                    lastUpdated: new Date()
                }
            };

            const updateData: Partial<Tenant> = {
                scripts: updatedScripts
            };

            await this.tenantStore.updateTenant(this.config.tenantId, updateData);
        });
    }

    /**
     * Get current save status for a script
     */
    getStatus(scriptKey: string): ScriptAutoSaveStatus {
        // This would need to be tracked in a reactive way
        return 'idle';
    }

    /**
     * Check if there are pending saves
     */
    hasPendingSaves(): boolean {
        return this.saveTimeouts.size > 0;
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<ScriptAutoSaveConfig>) {
        this.config = { ...this.config, ...newConfig };
    }
}

/**
 * Composable for using script auto-save service
 */
export function useScriptAutoSave(
    tenantId: string, 
    config?: Partial<ScriptAutoSaveConfig>
) {
    const autoSaveService = new ScriptAutoSaveService(tenantId, config);
    const saveStatuses = ref<Record<string, ScriptAutoSaveStatus>>({});

    // Track status changes
    const trackStatus = (scriptKey: string) => {
        autoSaveService.onStatusChange(scriptKey, (status) => {
            saveStatuses.value[scriptKey] = status;
        });
    };

    return {
        autoSaveService,
        saveStatuses,
        
        // Convenience methods
        autoSaveScript: (scriptKey: string, scriptConfig: ScriptConfig) => {
            trackStatus(scriptKey);
            return autoSaveService.autoSaveScript(scriptKey, scriptConfig);
        },
        
        autoSaveScripts: (scripts: ScriptsCollection) => {
            return autoSaveService.autoSaveScripts(scripts);
        },
        
        deleteScript: (scriptKey: string) => {
            return autoSaveService.deleteScript(scriptKey);
        },
        
        forceSave: (scriptKey: string, scriptConfig: ScriptConfig) => {
            return autoSaveService.forceSave(scriptKey, scriptConfig);
        },
        
        onStatusChange: (scriptKey: string, callback: (status: ScriptAutoSaveStatus) => void) => {
            trackStatus(scriptKey);
            return autoSaveService.onStatusChange(scriptKey, callback);
        },
        
        offStatusChange: (scriptKey: string) => {
            return autoSaveService.offStatusChange(scriptKey);
        },
        
        cancel: (scriptKey: string) => {
            return autoSaveService.cancel(scriptKey);
        },
        
        cancelAll: () => {
            return autoSaveService.cancelAll();
        },
        
        hasPendingSaves: () => {
            return autoSaveService.hasPendingSaves();
        }
    };
}
