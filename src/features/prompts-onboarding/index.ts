// Main feature exports
export { default as OnboardingPrompts } from './OnboardingPrompts.vue';

// Component exports
export { OpenAiParametersConfig, ChatbotPromptEditor, LeadAnalysisPromptEditor, FollowupPromptEditor } from './components';

// Service exports
export { AutoSaveService, useAutoSave, useReactiveAutoSave } from './services';

// Type exports
export type { OpenAIParameters, ChatPromptConfig, AnalysisPromptConfig, FollowupPromptConfig, TenantPromptConfigs, OnboardingStepPrompts, ParameterConfig, AutoSaveConfig, ValidationResult, PromptEditorProps, OpenAIParametersProps } from './types';

export type { AutoSaveStatus } from './services';

// Constants exports
export { DEFAULT_OPENAI_PARAMETERS, PARAMETER_CONFIGS, ONBOARDING_STEPS } from './types';
