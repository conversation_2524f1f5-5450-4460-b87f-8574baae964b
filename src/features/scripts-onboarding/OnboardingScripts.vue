<template>
    <OnboardingLayout>
        <!-- Header -->
        <template #header>
            <OnboardingHeader
                title="AI Scripts Setup"
                description="Create personalized scripts for your AI to use when communicating with leads"
                icon="pi pi-file-edit"
                :show-actions="true"
                :show-back-button="currentStep > 0"
                :show-close-button="true"
                :disabled="isLoading"
                @back="previousStep"
                @close="$emit('close')"
            />
        </template>

        <!-- Steps -->
        <template #steps>
            <OnboardingSteps :steps="formattedSteps" :current-step="currentStep" :allow-step-navigation="false" />
        </template>

        <!-- Main Content -->
        <OnboardingCard variant="default" size="large">
            <!-- Step 1: Welcome -->
            <div v-if="currentStep === 0" class="text-center space-y-6">
                <div>
                    <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-3">Welcome to AI Scripts!</h2>
                    <p class="text-surface-600 dark:text-surface-400 text-lg mb-6 max-w-2xl mx-auto">Let's create personalized scripts that your AI will use to communicate with leads based on their actions and status.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="pi pi-clock text-blue-600"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Save Time</div>
                            <div class="feature-description">Save time by automating routine tasks</div>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="pi pi-heart text-blue-600"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Personal Touch</div>
                            <div class="feature-description">Customize messages to match your brand voice</div>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="pi pi-chart-line text-purple-600"></i>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Better Results</div>
                            <div class="feature-description">Get better results with personalized messaging</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Script Creation -->
            <div v-else-if="currentStep === 1" class="script-creation-step">
                <LeadActionsScriptManager :disabled="isLoading" :auto-save="true" @scripts-change="onScriptsChange" @progress-change="onProgressChange" @configure-lead-actions="onConfigureLeadActions" @continue="nextStep" />
            </div>

            <!-- Step 3: Review & Test -->
            <div v-else-if="currentStep === 2" class="review-step">
                <Card>
                    <template #header>
                        <div class="card-header p-4">
                            <h2 class="text-xl font-semibold mb-2">Review Your Scripts</h2>
                            <p class="text-600">Review and test your scripts before completing the setup</p>
                        </div>
                    </template>

                    <template #content>
                        <div class="review-content">
                            <!-- Scripts Summary -->
                            <div class="scripts-summary mb-4">
                                <div class="flex justify-content-between align-items-center mb-3">
                                    <h3 class="font-semibold">Scripts Created: {{ Object.keys(currentScripts).length }}</h3>
                                    <Button label="Edit Scripts" icon="pi pi-pencil" severity="secondary" @click="previousStep" />
                                </div>

                                <div class="scripts-list">
                                    <div v-for="(script, actionKey) in currentScripts" :key="actionKey" class="script-summary-item p-3 border-1 border-200 border-round mb-2">
                                        <div class="flex justify-content-between align-items-start">
                                            <div class="flex-1">
                                                <h4 class="font-semibold mb-1 capitalize">{{ actionKey }}</h4>
                                                <p class="text-600 text-sm line-height-3">
                                                    {{ getScriptPreview(script.content) }}
                                                </p>
                                                <div class="mt-2">
                                                    <span class="text-xs text-500">Variables: </span>
                                                    <Tag v-for="variable in script.variables" :key="variable" :value="`[${variable}]`" severity="info" class="text-xs mr-1" />
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <i :class="script.isValid !== false ? 'pi pi-check-circle text-green-500' : 'pi pi-exclamation-triangle text-red-500'"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Test Variables -->
                            <div class="test-section">
                                <h3 class="font-semibold mb-3">Test Your Scripts</h3>
                                <p class="text-600 text-sm mb-3">Enter test values to see how your scripts will look with real data:</p>

                                <div class="test-variables grid">
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText id="test-customer-name" v-model="testVariables.customer_name" class="w-full" />
                                            <label for="test-customer-name">Customer Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText id="test-agent-name" v-model="testVariables.agent_name" class="w-full" />
                                            <label for="test-agent-name">Agent Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText id="test-company-name" v-model="testVariables.company_name" class="w-full" />
                                            <label for="test-company-name">Company Name</label>
                                        </FloatLabel>
                                    </div>
                                    <div class="col-12 md:col-6">
                                        <FloatLabel>
                                            <InputText id="test-service-type" v-model="testVariables.service_type" class="w-full" />
                                            <label for="test-service-type">Service Type</label>
                                        </FloatLabel>
                                    </div>
                                </div>

                                <!-- Script Preview -->
                                <div v-if="selectedPreviewScript" class="script-preview mt-4">
                                    <div class="flex justify-content-between align-items-center mb-2">
                                        <h4 class="font-semibold">Preview:</h4>
                                        <Dropdown v-model="selectedPreviewScript" :options="previewOptions" option-label="label" option-value="key" placeholder="Select script to preview" class="w-12rem" />
                                    </div>
                                    <div class="preview-content p-3 bg-blue-50 border-round border-1 border-blue-200">
                                        <pre class="text-sm text-700 m-0 whitespace-pre-wrap">{{ getPreviewContent() }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </Card>
            </div>

            <!-- Step 4: Complete -->
            <div v-else-if="currentStep === 3" class="complete-step">
                <Card class="text-center">
                    <template #content>
                        <div class="complete-content py-6">
                            <div class="success-icon mb-4">
                                <i class="pi pi-check-circle text-6xl text-green-500"></i>
                            </div>
                            <h2 class="text-2xl font-semibold mb-3">Scripts Setup Complete!</h2>
                            <p class="text-600 text-lg mb-4 max-w-30rem mx-auto">Your AI scripts are now ready to use. Your AI will automatically use these scripts when communicating with leads based on their actions.</p>

                            <div class="completion-stats grid max-w-30rem mx-auto mb-4">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="text-2xl font-bold text-primary">{{ Object.keys(currentScripts).length }}</div>
                                        <div class="text-sm text-600">Scripts Created</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="text-2xl font-bold text-green-500">{{ totalVariables }}</div>
                                        <div class="text-sm text-600">Variables Used</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </Card>
            </div>

            <!-- Navigation Footer -->
            <template #footer>
                <div class="flex justify-between items-center">
                    <div class="text-sm text-surface-600 dark:text-surface-400">Step {{ currentStep + 1 }} of {{ onboardingSteps.length }}</div>

                    <div class="flex gap-2">
                        <Button v-if="currentStep > 0 && currentStep < 3" label="Previous" icon="pi pi-arrow-left" severity="secondary" @click="previousStep" :disabled="isLoading" />
                        <Button v-if="currentStep < 2" label="Next" icon="pi pi-arrow-right" iconPos="right" @click="nextStep" :disabled="isLoading || (currentStep === 1 && !canProceedFromScripts)" />
                        <Button v-else-if="currentStep === 2" label="Complete Setup" icon="pi pi-check" iconPos="right" @click="completeOnboarding" :disabled="isLoading" />
                        <Button v-else-if="currentStep === 3" label="Finish" icon="pi pi-check" @click="$emit('complete', currentScripts)" />
                    </div>
                </div>
            </template>
        </OnboardingCard>
    </OnboardingLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingSteps, OnboardingCard, FeatureIcon, type OnboardingStep } from '@/shared/components/onboarding';

// Feature components
import LeadActionsScriptManager from './components/LeadActionsScriptManager.vue';
import { SCRIPT_ONBOARDING_STEPS } from './types';
import type { ScriptsCollection, ScriptOnboardingStep, ScriptOnboardingEvents } from './types';

// Emits
const emit = defineEmits<
    ScriptOnboardingEvents & {
        close: [];
    }
>();

// Composables
const toast = useToast();
const authStore = useAuthStore();

// State
const currentStep = ref(0);
const isLoading = ref(false);
const currentScripts = ref<ScriptsCollection>({});
const scriptProgress = ref(0);

const onboardingSteps = ref<ScriptOnboardingStep[]>([...SCRIPT_ONBOARDING_STEPS]);

// Test variables for preview
const testVariables = ref({
    customer_name: 'John Smith',
    agent_name: 'Sarah',
    company_name: 'Your Company',
    service_type: 'Kitchen Installation'
});

const selectedPreviewScript = ref<string | null>(null);

// Computed
const canProceedFromScripts = computed(() => {
    return Object.keys(currentScripts.value).length > 0 && scriptProgress.value >= 50;
});

const previewOptions = computed(() => {
    return Object.keys(currentScripts.value).map((key) => ({
        key,
        label: key.charAt(0).toUpperCase() + key.slice(1)
    }));
});

const totalVariables = computed(() => {
    const allVariables = new Set<string>();
    Object.values(currentScripts.value).forEach((script) => {
        script.variables.forEach((variable) => allVariables.add(variable));
    });
    return allVariables.size;
});

// Formatted steps for the new OnboardingSteps component
const formattedSteps = computed<OnboardingStep[]>(() =>
    onboardingSteps.value.map((step, index) => ({
        id: step.id,
        title: step.title,
        description: step.description,
        icon: step.icon || 'pi pi-circle',
        completed: step.completed
    }))
);

// Methods
const nextStep = () => {
    if (currentStep.value < onboardingSteps.value.length - 1) {
        onboardingSteps.value[currentStep.value].completed = true;
        currentStep.value++;
        onboardingSteps.value[currentStep.value].active = true;

        emit('step-change', onboardingSteps.value[currentStep.value]);

        // Set default preview script when entering review step
        if (currentStep.value === 2 && Object.keys(currentScripts.value).length > 0) {
            selectedPreviewScript.value = Object.keys(currentScripts.value)[0];
        }
    }
};

const previousStep = () => {
    if (currentStep.value > 0) {
        onboardingSteps.value[currentStep.value].active = false;
        currentStep.value--;
        onboardingSteps.value[currentStep.value].active = true;

        emit('step-change', onboardingSteps.value[currentStep.value]);
    }
};

const completeOnboarding = () => {
    onboardingSteps.value[currentStep.value].completed = true;
    nextStep();
};

const onScriptsChange = (scripts: ScriptsCollection) => {
    currentScripts.value = scripts;
    emit('scripts-change', scripts);
};

const onProgressChange = (progress: number) => {
    scriptProgress.value = progress;
    emit('progress-change', progress);
};

const onConfigureLeadActions = () => {
    toast.add({
        severity: 'info',
        summary: 'Configure Lead Actions',
        detail: 'Please configure your lead actions first in the settings',
        life: 5000
    });
};

const getScriptPreview = (content: string): string => {
    const maxLength = 100;
    const stripped = content.replace(/<[^>]*>/g, '').trim();
    return stripped.length > maxLength ? stripped.substring(0, maxLength) + '...' : stripped;
};

const getPreviewContent = (): string => {
    if (!selectedPreviewScript.value || !currentScripts.value[selectedPreviewScript.value]) {
        return '';
    }

    let content = currentScripts.value[selectedPreviewScript.value].content;

    // Replace variables with test values
    Object.entries(testVariables.value).forEach(([key, value]) => {
        const regex = new RegExp(`\\[${key}\\]`, 'g');
        content = content.replace(regex, value);
    });

    // Remove HTML tags for preview
    return content.replace(/<[^>]*>/g, '');
};

// Lifecycle
onMounted(() => {
    onboardingSteps.value[0].active = true;
});
</script>

<style scoped>
/* Custom styles for scripts onboarding specific elements */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

/* Script preview styling */
.script-summary-item {
    @apply transition-all duration-200 hover:bg-surface-50 dark:hover:bg-surface-800;
}

.script-preview {
    @apply bg-surface-50 dark:bg-surface-800 border border-surface-200 dark:border-surface-700 rounded-lg p-3;
}

/* Form styling */
.test-variables .p-float-label {
    @apply mb-4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .grid.grid-cols-1.md\\:grid-cols-3 {
        grid-template-columns: 1fr;
    }

    .flex.gap-2 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .flex.gap-2 .p-button {
        width: 100%;
    }
}

.feature-item {
    border-radius: 8px;
    padding: 1.25rem;
    border: 1px solid var(--surface-200);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.feature-icon {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin: 0 auto 1rem auto;
    background: var(--surface-100);
    border: 2px solid var(--surface-200);
}
.feature-icon i {
    font-size: 2rem;
}
.feature-content {
    text-align: center;
}

.feature-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.feature-description {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}
</style>
