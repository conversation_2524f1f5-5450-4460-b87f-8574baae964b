import AppLayout from '@/layout/AppLayout.vue';
import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        component: AppLayout,
        meta: { requiresAuth: true, maintenanceMode: true },
        children: [
            {
                path: '/dashboard',
                name: 'test-dashboard',
                component: () => import('@/views/Dashboard.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/',
                name: 'dashboard',
                component: () => import('@/entities/lead/ui/dashboard/Dashboard.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/leads/:id',
                name: 'leads',
                component: () => import('@/entities/lead/ui/components/LeadList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/bots/:id',
                name: 'bots',
                component: () => import('@/entities/lead/ui/components/BotPerformance.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/followups',
                name: 'followups',
                component: () => import('@/entities/lead/ui/components/FollowupList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/users',
                name: 'users',
                component: () => import('@/entities/user/ui/components/UserList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/tenants',
                name: 'tenants',
                component: () => import('@/entities/tenant/ui/components/TenantList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/tenants/:tenantId/users',
                name: 'tenant-users',
                component: () => import('@/entities/tenant/ui/components/TenantUserList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true },
                props: true
            },
            {
                path: '/setup',
                name: 'setup',
                component: () => import('@/features/onboarding-setup/OnboardingSetup.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            }
        ]
    },
    {
        path: '/landing',
        name: 'landing',
        component: () => import('@/views/pages/Landing.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/book-appointment',
        name: 'book-appointment',
        component: () => import('@/entities/appointments/ui/components/LeadAppointment.vue'),
        meta: { accessible: true, requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/notfound',
        name: 'notfound-auth',
        component: () => import('@/entities/auth/ui/components/NotFound.vue'),
        meta: { requiresAuth: true, maintenanceMode: true }
    },
    {
        path: '/notenant',
        name: 'notenant',
        component: () => import('@/entities/auth/ui/components/NotTenant.vue'),
        meta: { requiresAuth: true, maintenanceMode: true }
    },
    {
        path: '/notfound',
        name: 'notfound-public',
        component: () => import('@/entities/auth/ui/components/NotFound.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/entities/auth/ui/components/Login.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('@/entities/auth/ui/components/CompanyRegistration.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/privacy-policy',
        name: 'privacy-policy',
        component: () => import('@/entities/auth/ui/components/PrivacyPolicy.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/auth/access',
        name: 'accessDenied',
        component: () => import('@/entities/auth/ui/components/Access.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/auth/error',
        name: 'error',
        component: () => import('@/entities/auth/ui/components/Error.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    }
];
export default routes;
