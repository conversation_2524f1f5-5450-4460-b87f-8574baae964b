<template>
    <div class="mobile-navigation" :class="navigationClasses">
        <!-- Desktop Navigation -->
        <div class="desktop-nav">
            <slot name="desktop">
                <div class="flex justify-between items-center">
                    <slot name="left"></slot>
                    <slot name="center"></slot>
                    <slot name="right"></slot>
                </div>
            </slot>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-nav">
            <!-- Mobile Header -->
            <div v-if="showMobileHeader" class="mobile-header">
                <slot name="mobile-header">
                    <div class="flex justify-between items-center">
                        <Button
                            v-if="showBackButton"
                            icon="pi pi-arrow-left"
                            severity="secondary"
                            text
                            @click="$emit('back')"
                            :disabled="disabled"
                            aria-label="Go back"
                        />
                        <div class="mobile-title">
                            <slot name="mobile-title">{{ title }}</slot>
                        </div>
                        <Button
                            v-if="showCloseButton"
                            icon="pi pi-times"
                            severity="secondary"
                            text
                            @click="$emit('close')"
                            :disabled="disabled"
                            aria-label="Close"
                        />
                    </div>
                </slot>
            </div>

            <!-- Mobile Footer Navigation -->
            <div v-if="showMobileFooter" class="mobile-footer">
                <slot name="mobile-footer">
                    <div class="mobile-nav-buttons">
                        <Button
                            v-if="showPreviousButton"
                            :label="previousLabel"
                            icon="pi pi-chevron-left"
                            severity="secondary"
                            outlined
                            @click="$emit('previous')"
                            :disabled="disabled || !canGoPrevious"
                            class="flex-1"
                        />
                        <div v-if="showStepIndicator" class="step-indicator">
                            <span class="step-text">{{ currentStep + 1 }} / {{ totalSteps }}</span>
                            <div class="step-progress">
                                <div 
                                    class="step-progress-fill" 
                                    :style="{ width: `${progressPercentage}%` }"
                                ></div>
                            </div>
                        </div>
                        <Button
                            v-if="showNextButton"
                            :label="nextLabel"
                            :icon="isLastStep ? 'pi pi-check' : 'pi pi-chevron-right'"
                            :iconPos="isLastStep ? 'left' : 'right'"
                            @click="$emit(isLastStep ? 'complete' : 'next')"
                            :disabled="disabled || !canGoNext"
                            :severity="isLastStep ? 'success' : 'primary'"
                            class="flex-1"
                        />
                    </div>
                </slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Button from 'primevue/button';

// Props
interface Props {
    title?: string;
    currentStep?: number;
    totalSteps?: number;
    showBackButton?: boolean;
    showCloseButton?: boolean;
    showPreviousButton?: boolean;
    showNextButton?: boolean;
    showStepIndicator?: boolean;
    showMobileHeader?: boolean;
    showMobileFooter?: boolean;
    canGoPrevious?: boolean;
    canGoNext?: boolean;
    disabled?: boolean;
    previousLabel?: string;
    nextLabel?: string;
    sticky?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    currentStep: 0,
    totalSteps: 1,
    showBackButton: false,
    showCloseButton: false,
    showPreviousButton: true,
    showNextButton: true,
    showStepIndicator: true,
    showMobileHeader: true,
    showMobileFooter: true,
    canGoPrevious: true,
    canGoNext: true,
    disabled: false,
    previousLabel: 'Previous',
    nextLabel: 'Next',
    sticky: true
});

// Emits
const emit = defineEmits<{
    back: [];
    close: [];
    previous: [];
    next: [];
    complete: [];
}>();

// Computed
const navigationClasses = computed(() => {
    return {
        'navigation-sticky': props.sticky
    };
});

const progressPercentage = computed(() => {
    if (props.totalSteps <= 1) return 100;
    return (props.currentStep / (props.totalSteps - 1)) * 100;
});

const isLastStep = computed(() => {
    return props.currentStep >= props.totalSteps - 1;
});
</script>

<style scoped>
.mobile-navigation {
    width: 100%;
}

/* Desktop Navigation */
.desktop-nav {
    display: block;
}

.mobile-nav {
    display: none;
}

/* Mobile Navigation */
@media (max-width: 767px) {
    .desktop-nav {
        display: none;
    }
    
    .mobile-nav {
        display: block;
    }
}

/* Mobile Header */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border-bottom: 1px solid var(--surface-border);
    padding: 1rem;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dark .mobile-header {
    background: rgba(30, 30, 30, 0.95);
    border-bottom-color: var(--surface-700);
}

.mobile-title {
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--text-color);
    text-align: center;
    flex: 1;
    margin: 0 1rem;
}

/* Mobile Footer */
.mobile-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border-top: 1px solid var(--surface-border);
    padding: 1rem;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
}

.dark .mobile-footer {
    background: rgba(30, 30, 30, 0.95);
    border-top-color: var(--surface-700);
}

.mobile-nav-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Step Indicator */
.step-indicator {
    flex: 1;
    text-align: center;
    min-width: 80px;
}

.step-text {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    display: block;
    margin-bottom: 0.5rem;
}

.step-progress {
    height: 4px;
    background: var(--surface-300);
    border-radius: 2px;
    overflow: hidden;
}

.step-progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Sticky Navigation */
.navigation-sticky .mobile-header,
.navigation-sticky .mobile-footer {
    position: fixed;
}

/* Button Improvements */
.mobile-nav-buttons .p-button {
    min-height: 44px;
    font-weight: 600;
}

.mobile-nav-buttons .p-button.flex-1 {
    flex: 1;
    min-width: 0;
}

/* Safe area support */
.mobile-header {
    padding-top: calc(1rem + env(safe-area-inset-top));
}

.mobile-footer {
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
}

/* Landscape mode adjustments */
@media (max-width: 767px) and (orientation: landscape) {
    .mobile-header {
        padding-top: calc(0.5rem + env(safe-area-inset-top));
        padding-bottom: 0.5rem;
    }
    
    .mobile-footer {
        padding-top: 0.5rem;
        padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
    }
    
    .mobile-title {
        font-size: 1rem;
    }
    
    .step-text {
        font-size: 0.75rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .mobile-header,
    .mobile-footer {
        border-width: 2px;
    }
    
    .step-progress {
        border: 1px solid var(--text-color);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .step-progress-fill {
        transition: none;
    }
}

/* Focus improvements */
.mobile-nav-buttons .p-button:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading state */
.mobile-navigation.loading .mobile-nav-buttons .p-button {
    opacity: 0.6;
    pointer-events: none;
}

/* Error state */
.mobile-navigation.error .step-progress-fill {
    background: var(--red-500);
}

/* Success state */
.mobile-navigation.success .step-progress-fill {
    background: var(--green-500);
}
</style>
