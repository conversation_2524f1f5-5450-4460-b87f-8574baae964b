// OpenAI Parameters Interface
export interface OpenAIParameters {
    temperature: number;
    frequency_penalty: number;
    presence_penalty: number;
    top_p: number;
}

// Prompt Configuration Interfaces
export interface ChatPromptConfig {
    prompt: string;
    frequency_penalty: number;
    presence_penalty: number;
    temperature: number;
    top_p: number;
}

export interface AnalysisPromptConfig {
    prompt: string;
    frequency_penalty: number;
    presence_penalty: number;
    temperature: number;
    top_p: number;
}

export interface FollowupPromptConfig {
    frequency_penalty: number;
    presence_penalty: number;
    temperature: number;
    top_p: number;
}

// Extended Tenant Properties
export interface TenantPromptConfigs {
    chat?: {
        prompt?: ChatPromptConfig;
    };
    analysis?: {
        prompt?: AnalysisPromptConfig;
    };
    followup?: FollowupPromptConfig & {
        prompt?: string;
    };
}

// Onboarding Step Interface
export interface OnboardingStepPrompts {
    id: number;
    title: string;
    description: string;
    icon: string;
    completed: boolean;
}

// Parameter Configuration for UI
export interface ParameterConfig {
    key: keyof OpenAIParameters;
    label: string;
    description: string;
    min: number;
    max: number;
    step: number;
    defaultValue: number;
}

// Auto-save Configuration
export interface AutoSaveConfig {
    enabled: boolean;
    debounceMs: number;
    tenantId: string;
}

// Validation Interface
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

// Component Props Interfaces
export interface PromptEditorProps {
    modelValue: string;
    parameters: OpenAIParameters;
    title: string;
    description: string;
    placeholder?: string;
    disabled?: boolean;
    autoSave?: AutoSaveConfig;
}

export interface OpenAIParametersProps {
    modelValue: OpenAIParameters;
    disabled?: boolean;
    showLabels?: boolean;
    compact?: boolean;
}

// Default Values
export const DEFAULT_OPENAI_PARAMETERS: OpenAIParameters = {
    temperature: 0.7,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
    top_p: 1.0
};

export const PARAMETER_CONFIGS: ParameterConfig[] = [
    {
        key: 'temperature',
        label: 'Creativity Level',
        description: 'Controls randomness in responses. Higher values make output more creative and varied, lower values make it more focused and deterministic.',
        min: 0,
        max: 2,
        step: 0.1,
        defaultValue: 0.7
    },
    {
        key: 'frequency_penalty',
        label: 'Repetition Reduction',
        description: 'Reduces likelihood of repeating the same words or phrases. Higher values encourage more diverse vocabulary.',
        min: -2,
        max: 2,
        step: 0.1,
        defaultValue: 0.0
    },
    {
        key: 'presence_penalty',
        label: 'Topic Diversity',
        description: 'Encourages the AI to talk about new topics. Higher values make the AI more likely to introduce new subjects.',
        min: -2,
        max: 2,
        step: 0.1,
        defaultValue: 0.0
    },
    {
        key: 'top_p',
        label: 'Focus Control',
        description: 'Controls diversity via nucleus sampling. Lower values make responses more focused, higher values allow more variety.',
        min: 0,
        max: 1,
        step: 0.05,
        defaultValue: 1.0
    }
];

// Onboarding Steps Configuration
export const ONBOARDING_STEPS: OnboardingStepPrompts[] = [
    {
        id: 1,
        title: 'Chatbot Prompts',
        description: 'Configure AI responses for customer interactions',
        icon: 'pi pi-comments',
        completed: false
    },
    {
        id: 2,
        title: 'Lead Analysis',
        description: 'Set up automated lead qualification prompts',
        icon: 'pi pi-chart-line',
        completed: false
    },
    {
        id: 3,
        title: 'Follow-up Messages',
        description: 'Configure automated follow-up communications',
        icon: 'pi pi-send',
        completed: false
    }
];
