<template>
    <div class="onboarding-example">
        <!-- Navigation -->
        <div class="example-nav mb-6">
            <div class="flex gap-3">
                <Button
                    label="Full Onboarding"
                    :class="{ 'p-button-outlined': currentView !== 'onboarding' }"
                    @click="currentView = 'onboarding'"
                />
                <Button
                    label="Feature Selection"
                    :class="{ 'p-button-outlined': currentView !== 'selection' }"
                    @click="currentView = 'selection'"
                />
                <Button
                    label="Dashboard View"
                    :class="{ 'p-button-outlined': currentView !== 'dashboard' }"
                    @click="currentView = 'dashboard'"
                />
            </div>
        </div>

        <!-- Full Onboarding Example -->
        <div v-if="currentView === 'onboarding'">
            <Card class="mb-4">
                <template #header>
                    <div class="p-4">
                        <h2 class="text-xl font-bold">Full Onboarding Experience</h2>
                        <p class="text-surface-600 dark:text-surface-400">
                            Complete onboarding flow with all features
                        </p>
                    </div>
                </template>
                <template #content>
                    <OnboardingSetup
                        :auto-start="false"
                        redirect-on-complete="/dashboard"
                        @complete="handleOnboardingComplete"
                        @exit="handleOnboardingExit"
                        @feature-complete="handleFeatureComplete"
                    />
                </template>
            </Card>
        </div>

        <!-- Feature Selection Example -->
        <div v-if="currentView === 'selection'">
            <Card class="mb-4">
                <template #header>
                    <div class="p-4">
                        <h2 class="text-xl font-bold">Custom Feature Selection</h2>
                        <p class="text-surface-600 dark:text-surface-400">
                            Let users choose which features to set up
                        </p>
                    </div>
                </template>
                <template #content>
                    <OnboardingSetup
                        :auto-start="true"
                        :selected-features="preSelectedFeatures"
                        @complete="handleCustomOnboardingComplete"
                    />
                </template>
            </Card>
        </div>

        <!-- Dashboard Example -->
        <div v-if="currentView === 'dashboard'">
            <Card class="mb-4">
                <template #header>
                    <div class="p-4">
                        <h2 class="text-xl font-bold">Onboarding Dashboard</h2>
                        <p class="text-surface-600 dark:text-surface-400">
                            Overview and management of onboarding progress
                        </p>
                    </div>
                </template>
                <template #content>
                    <OnboardingDashboard
                        :features="mockFeatures"
                        :started-at="mockStartDate"
                        @continue-onboarding="handleContinueOnboarding"
                        @start-feature="handleStartFeature"
                        @configure-feature="handleConfigureFeature"
                        @retry-feature="handleRetryFeature"
                        @export-report="handleExportReport"
                        @go-to-dashboard="handleGoToDashboard"
                    />
                </template>
            </Card>
        </div>

        <!-- Event Log -->
        <Card v-if="eventLog.length > 0" class="mt-6">
            <template #header>
                <div class="p-4">
                    <h3 class="text-lg font-semibold">Event Log</h3>
                    <Button
                        label="Clear Log"
                        icon="pi pi-trash"
                        severity="secondary"
                        outlined
                        size="small"
                        @click="eventLog = []"
                    />
                </div>
            </template>
            <template #content>
                <div class="space-y-2 max-h-60 overflow-y-auto">
                    <div
                        v-for="(event, index) in eventLog"
                        :key="index"
                        class="event-item p-3 bg-surface-50 dark:bg-surface-800 rounded border-l-4"
                        :class="getEventClass(event.type)"
                    >
                        <div class="flex justify-between items-start">
                            <div>
                                <div class="font-medium">{{ event.type }}</div>
                                <div class="text-sm text-surface-600 dark:text-surface-400">
                                    {{ event.message }}
                                </div>
                            </div>
                            <div class="text-xs text-surface-500">
                                {{ formatTime(event.timestamp) }}
                            </div>
                        </div>
                        <div v-if="event.data" class="mt-2">
                            <details class="text-xs">
                                <summary class="cursor-pointer text-surface-600 dark:text-surface-400">
                                    View Data
                                </summary>
                                <pre class="mt-1 p-2 bg-surface-100 dark:bg-surface-700 rounded text-xs overflow-x-auto">{{ JSON.stringify(event.data, null, 2) }}</pre>
                            </details>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useToast } from 'primevue/usetoast';

// PrimeVue Components
import Card from 'primevue/card';
import Button from 'primevue/button';

// Onboarding Components
import { OnboardingSetup, OnboardingDashboard } from '@/features/onboarding-setup';
import { ONBOARDING_FEATURES, FEATURE_ORDER } from '@/features/onboarding-setup/constants';
import type { OnboardingFeature } from '@/features/onboarding-setup/types';

// Composables
const toast = useToast();

// State
const currentView = ref<'onboarding' | 'selection' | 'dashboard'>('onboarding');
const eventLog = ref<Array<{
    type: string;
    message: string;
    timestamp: Date;
    data?: any;
}>>([]);

// Pre-selected features for custom onboarding
const preSelectedFeatures = ref(['companyInfo', 'userInfo', 'calendar', 'prompts']);

// Mock data for dashboard
const mockStartDate = ref(new Date(Date.now() - 1800000)); // 30 minutes ago

const mockFeatures = ref<OnboardingFeature[]>(
    FEATURE_ORDER.map((featureId, index) => {
        const config = ONBOARDING_FEATURES[featureId];
        return {
            id: featureId,
            name: featureId,
            title: config.title,
            description: config.description,
            icon: config.icon,
            color: config.color,
            category: config.category as any,
            priority: index,
            estimatedTime: config.estimatedTime,
            dependencies: [],
            component: featureId,
            isRequired: config.isRequired,
            isCompleted: index < 3, // First 3 features completed
            isSkipped: index === 4,  // Email feature skipped
            completedAt: index < 3 ? new Date(Date.now() - (3 - index) * 600000) : undefined
        };
    })
);

// Event Handlers
const addEvent = (type: string, message: string, data?: any) => {
    eventLog.value.unshift({
        type,
        message,
        timestamp: new Date(),
        data
    });
    
    // Keep only last 20 events
    if (eventLog.value.length > 20) {
        eventLog.value = eventLog.value.slice(0, 20);
    }
};

const handleOnboardingComplete = (data: any) => {
    addEvent('onboarding-complete', 'Full onboarding process completed', data);
    toast.add({
        severity: 'success',
        summary: 'Onboarding Complete',
        detail: 'All features have been set up successfully!',
        life: 5000
    });
};

const handleOnboardingExit = () => {
    addEvent('onboarding-exit', 'User exited the onboarding process');
    toast.add({
        severity: 'info',
        summary: 'Onboarding Exited',
        detail: 'You can resume setup anytime from the dashboard',
        life: 3000
    });
};

const handleFeatureComplete = (featureId: string, data: any) => {
    const featureName = ONBOARDING_FEATURES[featureId]?.title || featureId;
    addEvent('feature-complete', `${featureName} setup completed`, { featureId, data });
    toast.add({
        severity: 'success',
        summary: 'Feature Complete',
        detail: `${featureName} has been set up successfully`,
        life: 3000
    });
};

const handleCustomOnboardingComplete = (data: any) => {
    addEvent('custom-onboarding-complete', 'Custom onboarding with selected features completed', data);
    toast.add({
        severity: 'success',
        summary: 'Custom Setup Complete',
        detail: 'Selected features have been configured!',
        life: 5000
    });
};

const handleContinueOnboarding = () => {
    addEvent('continue-onboarding', 'User chose to continue onboarding from dashboard');
    currentView.value = 'onboarding';
};

const handleStartFeature = (feature: OnboardingFeature) => {
    addEvent('start-feature', `Starting setup for ${feature.title}`, { featureId: feature.id });
    toast.add({
        severity: 'info',
        summary: 'Starting Feature Setup',
        detail: `Setting up ${feature.title}`,
        life: 3000
    });
};

const handleConfigureFeature = (feature: OnboardingFeature) => {
    addEvent('configure-feature', `Reconfiguring ${feature.title}`, { featureId: feature.id });
    toast.add({
        severity: 'info',
        summary: 'Reconfiguring Feature',
        detail: `Updating ${feature.title} settings`,
        life: 3000
    });
};

const handleRetryFeature = (feature: OnboardingFeature) => {
    addEvent('retry-feature', `Retrying setup for ${feature.title}`, { featureId: feature.id });
    toast.add({
        severity: 'warn',
        summary: 'Retrying Feature',
        detail: `Attempting to set up ${feature.title} again`,
        life: 3000
    });
};

const handleExportReport = () => {
    addEvent('export-report', 'User exported onboarding progress report');
    toast.add({
        severity: 'info',
        summary: 'Report Exported',
        detail: 'Onboarding progress report has been downloaded',
        life: 3000
    });
};

const handleGoToDashboard = () => {
    addEvent('go-to-dashboard', 'User navigated to main dashboard');
    toast.add({
        severity: 'success',
        summary: 'Welcome to Dashboard',
        detail: 'You can now start using your configured platform!',
        life: 3000
    });
};

// Utility Functions
const getEventClass = (type: string) => {
    const classes = {
        'onboarding-complete': 'border-green-400',
        'custom-onboarding-complete': 'border-green-400',
        'feature-complete': 'border-blue-400',
        'onboarding-exit': 'border-orange-400',
        'continue-onboarding': 'border-primary-400',
        'start-feature': 'border-blue-400',
        'configure-feature': 'border-purple-400',
        'retry-feature': 'border-orange-400',
        'export-report': 'border-gray-400',
        'go-to-dashboard': 'border-green-400'
    };
    return classes[type as keyof typeof classes] || 'border-gray-400';
};

const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};
</script>

<style scoped>
.onboarding-example {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.example-nav {
    text-align: center;
}

.event-item {
    transition: all 0.2s ease;
}

.event-item:hover {
    transform: translateX(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .onboarding-example {
        padding: 1rem 0.5rem;
    }
    
    .example-nav .flex {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
