<template>
    <div 
        class="feature-icon"
        :class="iconClasses"
    >
        <i :class="icon" class="icon"></i>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
interface Props {
    icon: string;
    size?: 'small' | 'medium' | 'large' | 'xl';
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'gradient';
    shape?: 'circle' | 'rounded' | 'square';
}

const props = withDefaults(defineProps<Props>(), {
    size: 'medium',
    variant: 'primary',
    shape: 'rounded'
});

// Computed
const iconClasses = computed(() => {
    return {
        [`icon-${props.size}`]: true,
        [`icon-${props.variant}`]: true,
        [`icon-${props.shape}`]: true
    };
});
</script>

<style scoped>
.feature-icon {
    @apply flex items-center justify-center flex-shrink-0;
    @apply transition-all duration-200;
}

/* Sizes */
.icon-small {
    @apply w-8 h-8;
}

.icon-small .icon {
    @apply text-sm;
}

.icon-medium {
    @apply w-12 h-12;
}

.icon-medium .icon {
    @apply text-lg;
}

.icon-large {
    @apply w-16 h-16;
}

.icon-large .icon {
    @apply text-2xl;
}

.icon-xl {
    @apply w-20 h-20;
}

.icon-xl .icon {
    @apply text-4xl;
}

/* Shapes */
.icon-circle {
    @apply rounded-full;
}

.icon-rounded {
    @apply rounded-xl;
}

.icon-square {
    @apply rounded-none;
}

/* Variants */
.icon-primary {
    @apply bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400;
}

.icon-secondary {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-600 dark:text-surface-400;
}

.icon-success {
    @apply bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400;
}

.icon-warning {
    @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400;
}

.icon-error {
    @apply bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400;
}

.icon-info {
    @apply bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400;
}

.icon-gradient {
    @apply bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg;
}

/* Hover effects */
.feature-icon:hover {
    @apply transform scale-105;
}

.icon-gradient:hover {
    @apply shadow-xl;
}
</style>
