<template>
    <Card 
        class="onboarding-card"
        :class="cardClasses"
    >
        <!-- Header Slot -->
        <template v-if="$slots.header || title" #header>
            <slot name="header">
                <div v-if="title" class="card-header">
                    <div class="header-content">
                        <div v-if="icon" class="header-icon">
                            <i :class="icon" class="icon"></i>
                        </div>
                        <div class="header-text">
                            <h3 class="header-title">{{ title }}</h3>
                            <p v-if="subtitle" class="header-subtitle">{{ subtitle }}</p>
                        </div>
                    </div>
                    <div v-if="$slots.actions" class="header-actions">
                        <slot name="actions"></slot>
                    </div>
                </div>
            </slot>
        </template>

        <!-- Content -->
        <template #content>
            <div class="card-content" :class="contentClasses">
                <slot></slot>
            </div>
        </template>

        <!-- Footer Slot -->
        <template v-if="$slots.footer" #footer>
            <div class="card-footer">
                <slot name="footer"></slot>
            </div>
        </template>
    </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Card from 'primevue/card';

// Props
interface Props {
    title?: string;
    subtitle?: string;
    icon?: string;
    variant?: 'default' | 'elevated' | 'outlined' | 'success' | 'warning' | 'error' | 'info';
    size?: 'small' | 'medium' | 'large';
    centered?: boolean;
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    variant: 'default',
    size: 'medium',
    centered: false,
    loading: false
});

// Computed
const cardClasses = computed(() => {
    return {
        [`card-${props.variant}`]: true,
        [`card-${props.size}`]: true,
        'card-centered': props.centered,
        'card-loading': props.loading
    };
});

const contentClasses = computed(() => {
    return {
        'content-centered': props.centered
    };
});
</script>

<style scoped>
.onboarding-card {
    @apply w-full;
}

/* Card Variants */
.card-default {
    @apply shadow-sm border border-surface-200 dark:border-surface-700;
}

.card-elevated {
    @apply shadow-lg border-0;
}

.card-outlined {
    @apply shadow-none border-2 border-surface-300 dark:border-surface-600;
}

.card-success {
    @apply border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20;
}

.card-warning {
    @apply border-yellow-200 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/20;
}

.card-error {
    @apply border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/20;
}

.card-info {
    @apply border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20;
}

/* Card Sizes */
.card-small :deep(.p-card-content) {
    @apply p-4;
}

.card-medium :deep(.p-card-content) {
    @apply p-6;
}

.card-large :deep(.p-card-content) {
    @apply p-8;
}

/* Header Styles */
.card-header {
    @apply flex items-center justify-between p-6 pb-0;
}

.header-content {
    @apply flex items-center gap-4 flex-1;
}

.header-icon {
    @apply w-12 h-12 rounded-lg bg-primary-100 dark:bg-primary-900/30;
    @apply flex items-center justify-center flex-shrink-0;
}

.header-icon .icon {
    @apply text-xl text-primary-600 dark:text-primary-400;
}

.header-text {
    @apply flex-1 min-w-0;
}

.header-title {
    @apply text-xl font-semibold text-surface-900 dark:text-surface-0 mb-1;
}

.header-subtitle {
    @apply text-sm text-surface-600 dark:text-surface-400;
}

.header-actions {
    @apply flex items-center gap-2 flex-shrink-0;
}

/* Content Styles */
.card-content {
    @apply space-y-6;
}

.content-centered {
    @apply text-center;
}

/* Footer Styles */
.card-footer {
    @apply p-6 pt-0 flex items-center justify-end gap-3;
}

/* Centered Card */
.card-centered {
    @apply mx-auto;
}

.card-centered .card-content {
    @apply text-center;
}

/* Loading State */
.card-loading {
    @apply opacity-75 pointer-events-none;
}

.card-loading::after {
    content: '';
    @apply absolute inset-0 bg-surface-100/50 dark:bg-surface-800/50;
    @apply flex items-center justify-center;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%236366f1' d='M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z' opacity='.25'/%3E%3Cpath fill='%236366f1' d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        @apply flex-col items-start gap-4;
    }
    
    .header-content {
        @apply w-full;
    }
    
    .header-actions {
        @apply w-full justify-end;
    }
    
    .card-footer {
        @apply flex-col gap-3;
    }
}
</style>
