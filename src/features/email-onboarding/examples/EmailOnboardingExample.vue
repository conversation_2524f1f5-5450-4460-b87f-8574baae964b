<template>
    <div class="email-onboarding-example">
        <div class="mb-4">
            <h1 class="text-2xl font-bold mb-2">Email Integration Setup</h1>
            <p class="text-gray-600">Complete the email onboarding process to enable chatbot email responses.</p>
        </div>

        <!-- Show numbers-prompts-onboarding if not complete -->
        <OnboardingEmail v-if="!isOnboardingComplete" @complete="handleOnboardingComplete" />

        <!-- Show completion status if already complete -->
        <Card v-else class="text-center">
            <template #content>
                <div class="space-y-4">
                    <i class="pi pi-check-circle text-6xl text-green-500"></i>
                    <h3 class="text-xl font-semibold text-green-700">Email Integration Active</h3>
                    <p class="text-gray-600">Your email integration is set up and working properly.</p>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
                        <h4 class="font-medium text-green-800 mb-2">Current Configuration:</h4>
                        <div class="text-sm text-green-700 space-y-1 text-left">
                            <div><strong>Default Email:</strong> {{ tenant?.email?.defaultEmail }}</div>
                            <div><strong>Forwarding To:</strong> {{ forwardingEmail }}</div>
                            <div>
                                <strong>Status:</strong>
                                <span class="inline-flex items-center">
                                    <i class="pi pi-check text-green-600 mr-1"></i>
                                    Validated
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <Button label="Reconfigure" icon="pi pi-cog" severity="secondary" @click="reconfigureEmail" outlined />
                        <Button label="Test Integration" icon="pi pi-send" @click="testEmailIntegration" />
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant/store';
import { OnboardingEmail, EmailOnboardingService } from '../index';
import type { Tenant } from '@/entities/tenant/model';

// Stores
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// State
const tenant = ref<Tenant | null>(null);
const isLoading = ref(false);

// Computed
const isOnboardingComplete = computed(() => {
    return tenant.value ? EmailOnboardingService.isOnboardingComplete(tenant.value) : false;
});

const forwardingEmail = computed(() => {
    const userData = authStore.userData;
    if (userData?.tenantId) {
        return EmailOnboardingService.getForwardingEmail(userData.tenantId);
    }
    return '';
});

// Methods
const loadTenantData = async () => {
    try {
        isLoading.value = true;
        const userData = await authStore.getUserData();
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        const tenantData = await tenantStore.getTenant(userData.tenantId);
        if (!tenantData) {
            throw new Error('Tenant not found');
        }

        tenant.value = tenantData;
    } catch (error) {
        console.error('Error loading tenant data:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load tenant data.',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

const handleOnboardingComplete = () => {
    toast.add({
        severity: 'success',
        summary: 'Setup Complete',
        detail: 'Email integration has been successfully configured!',
        life: 5000
    });

    // Reload tenant data to reflect the changes
    loadTenantData();
};

const reconfigureEmail = () => {
    // Reset the tenant email configuration to restart numbers-prompts-onboarding
    tenant.value = {
        ...tenant.value!,
        email: {
            ...tenant.value!.email,
            validated: false
        }
    };
};

const testEmailIntegration = async () => {
    try {
        if (!tenant.value?.email?.defaultEmail) {
            throw new Error('No email configured');
        }

        const emailService = new EmailOnboardingService();
        await emailService.validateEmail(tenant.value.email.defaultEmail);

        toast.add({
            severity: 'info',
            summary: 'Test Email Sent',
            detail: 'A test email has been sent to verify your integration.',
            life: 5000
        });
    } catch (error) {
        console.error('Error testing email integration:', error);
        toast.add({
            severity: 'error',
            summary: 'Test Failed',
            detail: 'Failed to send test email. Please check your configuration.',
            life: 5000
        });
    }
};

// Lifecycle
onMounted(() => {
    loadTenantData();
});
</script>

<style scoped>
.email-onboarding-example {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}
</style>
