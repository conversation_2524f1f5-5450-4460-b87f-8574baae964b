<template>
    <div class="openai-parameters">
        <div class="mb-4">
            <h4 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">
                AI Behavior Settings
            </h4>
            <p class="text-sm text-surface-600 dark:text-surface-400 mb-4">
                Fine-tune how the AI responds by adjusting these parameters. Hover over each setting for detailed explanations.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div 
                v-for="config in parameterConfigs" 
                :key="config.key"
                class="parameter-group"
            >
                <div class="flex items-center justify-between mb-2">
                    <label 
                        :for="`param-${config.key}`"
                        class="block text-sm font-medium text-surface-900 dark:text-surface-0"
                    >
                        {{ config.label }}
                        <i 
                            class="pi pi-info-circle ml-1 text-surface-400 cursor-help"
                            v-tooltip.top="config.description"
                        ></i>
                    </label>
                    <span class="text-sm font-mono text-surface-600 dark:text-surface-400 bg-surface-100 dark:bg-surface-700 px-2 py-1 rounded">
                        {{ formatValue(localParameters[config.key]) }}
                    </span>
                </div>

                <Slider
                    :id="`param-${config.key}`"
                    v-model="localParameters[config.key]"
                    :min="config.min"
                    :max="config.max"
                    :step="config.step"
                    :disabled="disabled"
                    class="w-full"
                    @change="onParameterChange"
                />

                <div class="flex justify-between text-xs text-surface-500 dark:text-surface-400 mt-1">
                    <span>{{ config.min }}</span>
                    <span>{{ config.max }}</span>
                </div>

                <div v-if="!compact" class="mt-2">
                    <p class="text-xs text-surface-600 dark:text-surface-400">
                        {{ config.description }}
                    </p>
                </div>
            </div>
        </div>

        <div v-if="showPresets" class="mt-6 pt-4 border-t border-surface-200 dark:border-surface-700">
            <h5 class="text-sm font-medium text-surface-900 dark:text-surface-0 mb-3">
                Quick Presets
            </h5>
            <div class="flex flex-wrap gap-2">
                <Button
                    v-for="preset in presets"
                    :key="preset.name"
                    :label="preset.name"
                    size="small"
                    outlined
                    :disabled="disabled"
                    @click="applyPreset(preset.values)"
                    class="text-xs"
                />
            </div>
        </div>

        <div v-if="showReset" class="mt-4 flex justify-end">
            <Button
                label="Reset to Defaults"
                icon="pi pi-refresh"
                size="small"
                outlined
                severity="secondary"
                :disabled="disabled"
                @click="resetToDefaults"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import Slider from 'primevue/slider';
import Button from 'primevue/button';
import type { OpenAIParameters, OpenAIParametersProps } from '../types';
import { PARAMETER_CONFIGS, DEFAULT_OPENAI_PARAMETERS } from '../types';

// Props
const props = withDefaults(defineProps<OpenAIParametersProps & {
    showPresets?: boolean;
    showReset?: boolean;
}>(), {
    disabled: false,
    showLabels: true,
    compact: false,
    showPresets: true,
    showReset: true
});

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: OpenAIParameters];
    'change': [value: OpenAIParameters];
}>();

// Local state
const localParameters = ref<OpenAIParameters>({ ...props.modelValue });

// Computed
const parameterConfigs = computed(() => PARAMETER_CONFIGS);

// Presets for common use cases
const presets = computed(() => [
    {
        name: 'Conservative',
        values: { temperature: 0.3, frequency_penalty: 0.0, presence_penalty: 0.0, top_p: 0.8 }
    },
    {
        name: 'Balanced',
        values: { temperature: 0.7, frequency_penalty: 0.0, presence_penalty: 0.0, top_p: 1.0 }
    },
    {
        name: 'Creative',
        values: { temperature: 1.2, frequency_penalty: 0.5, presence_penalty: 0.3, top_p: 0.9 }
    },
    {
        name: 'Focused',
        values: { temperature: 0.5, frequency_penalty: 0.2, presence_penalty: 0.1, top_p: 0.7 }
    }
]);

// Methods
const formatValue = (value: number): string => {
    return value.toFixed(2);
};

const onParameterChange = () => {
    emit('update:modelValue', { ...localParameters.value });
    emit('change', { ...localParameters.value });
};

const applyPreset = (presetValues: Partial<OpenAIParameters>) => {
    localParameters.value = { ...localParameters.value, ...presetValues };
    onParameterChange();
};

const resetToDefaults = () => {
    localParameters.value = { ...DEFAULT_OPENAI_PARAMETERS };
    onParameterChange();
};

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
    localParameters.value = { ...newValue };
}, { deep: true });
</script>

<style scoped>
.parameter-group {
    @apply p-4 bg-surface-50 dark:bg-surface-800 rounded-lg border border-surface-200 dark:border-surface-700;
}

.parameter-group:hover {
    @apply border-primary-200 dark:border-primary-700;
}

:deep(.p-slider) {
    @apply bg-surface-200 dark:bg-surface-600;
}

:deep(.p-slider .p-slider-range) {
    @apply bg-primary-500;
}

:deep(.p-slider .p-slider-handle) {
    @apply bg-primary-500 border-primary-500;
}

:deep(.p-slider .p-slider-handle:hover) {
    @apply bg-primary-600 border-primary-600;
}
</style>
