<template>
    <div class="script-editor">
        <!-- Header with action info and save status -->
        <div class="flex justify-content-between align-items-center mb-3">
            <div class="flex align-items-center gap-2">
                <div
                    class="action-badge flex align-items-center gap-2 px-3 py-2 border-round"
                    :style="{
                        backgroundColor: actionConfig.bg_color,
                        color: actionConfig.text_color
                    }"
                >
                    <i :class="actionConfig.icon"></i>
                    <span class="font-semibold">{{ actionConfig.label }}</span>
                </div>
                <Button v-if="showVariableHelp" icon="pi pi-question-circle" severity="secondary" text @click="showHelp = true" v-tooltip.bottom="'Show available variables'" />
            </div>

            <!-- Save status indicator -->
            <div class="flex align-items-center gap-2">
                <div v-if="saveStatus !== 'idle'" class="flex align-items-center gap-2">
                    <ProgressSpinner v-if="saveStatus === 'saving'" style="width: 16px; height: 16px" strokeWidth="4" />
                    <i v-else-if="saveStatus === 'saved'" class="pi pi-check text-green-500"></i>
                    <i v-else-if="saveStatus === 'error'" class="pi pi-exclamation-triangle text-red-500"></i>
                    <span class="text-sm text-600">
                        {{ saveStatusText }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Editor -->
        <div class="editor-container">
            <Editor
                :model-value="modelValue"
                :placeholder="placeholder || `Create your ${actionConfig.label.toLowerCase()} script...`"
                :readonly="disabled || isLoading"
                editorStyle="height: 300px"
                class="w-full"
                :class="{ 'p-invalid': hasValidationErrors }"
                @text-change="onTextChange"
                @blur="onBlur"
            >
                <template #toolbar>
                    <span class="ql-formats">
                        <button class="ql-bold" v-tooltip.bottom="'Bold'"></button>
                        <button class="ql-italic" v-tooltip.bottom="'Italic'"></button>
                        <button class="ql-underline" v-tooltip.bottom="'Underline'"></button>
                        <button class="ql-strike" v-tooltip.bottom="'Strikethrough'"></button>
                    </span>
                    <span class="ql-formats">
                        <select class="ql-header" v-tooltip.bottom="'Header'">
                            <option value="1">Heading 1</option>
                            <option value="2">Heading 2</option>
                            <option value="3">Heading 3</option>
                            <option selected>Normal</option>
                        </select>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-list" value="ordered" v-tooltip.bottom="'Numbered List'"></button>
                        <button class="ql-list" value="bullet" v-tooltip.bottom="'Bullet List'"></button>
                        <button class="ql-indent" value="-1" v-tooltip.bottom="'Decrease Indent'"></button>
                        <button class="ql-indent" value="+1" v-tooltip.bottom="'Increase Indent'"></button>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-blockquote" v-tooltip.bottom="'Quote'"></button>
                        <button class="ql-code-block" v-tooltip.bottom="'Code Block'"></button>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-link" v-tooltip.bottom="'Insert Link'"></button>
                        <button class="ql-clean" v-tooltip.bottom="'Remove Formatting'"></button>
                    </span>
                </template>
            </Editor>
        </div>

        <!-- Validation feedback -->
        <div v-if="validationResult" class="mt-3">
            <!-- Errors -->
            <div v-if="validationResult.errors.length > 0" class="mb-2">
                <Message v-for="error in validationResult.errors" :key="error" severity="error" :closable="false" class="mb-1">
                    {{ error }}
                </Message>
            </div>

            <!-- Warnings -->
            <div v-if="validationResult.warnings.length > 0" class="mb-2">
                <Message v-for="warning in validationResult.warnings" :key="warning" severity="warn" :closable="false" class="mb-1">
                    {{ warning }}
                </Message>
            </div>

            <!-- Variables info -->
            <div v-if="validationResult.variables.length > 0" class="variables-info">
                <div class="text-sm text-600 mb-2">
                    <i class="pi pi-info-circle mr-1"></i>
                    Variables found: {{ validationResult.variables.length }}
                </div>
                <div class="flex flex-wrap gap-1">
                    <Tag v-for="variable in validationResult.variables" :key="variable" :value="`[${variable}]`" severity="info" class="text-xs" />
                </div>
            </div>
        </div>

        <!-- Variables Help Dialog -->
        <ScriptVariablesHelp v-if="showVariableHelp" :action-key="actionKey" :visible="showHelp" @update:visible="showHelp = $event" @insert-variable="insertVariable" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/entities/auth';
import { useScriptAutoSave } from '../services/scriptAutoSaveService';
import { useScriptValidation } from '../services/scriptValidationService';
import { DEFAULT_SCRIPT_VARIABLES } from '../types';
import ScriptVariablesHelp from './ScriptVariablesHelp.vue';
import type { ScriptEditorProps, ScriptConfig, ScriptValidationResult, ScriptAutoSaveStatus } from '../types';

// Props
interface Props extends ScriptEditorProps {}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    disabled: false,
    showVariableHelp: true
});

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: string];
    'script-change': [script: ScriptConfig];
    'validation-change': [result: ScriptValidationResult];
    'save-status-change': [status: ScriptAutoSaveStatus];
}>();

// Composables
const authStore = useAuthStore();
const userData = authStore.getUserData();
const tenantId = userData?.tenantId;

// Auto-save service
const { autoSaveScript, onStatusChange, offStatusChange } = useScriptAutoSave(tenantId || '', props.autoSave);

// Validation service
const { validateScript } = useScriptValidation();

// State
const isLoading = ref(false);
const showHelp = ref(false);
const saveStatus = ref<ScriptAutoSaveStatus>('idle');
const validationResult = ref<ScriptValidationResult | null>(null);

// Computed
const hasValidationErrors = computed(() => validationResult.value?.errors.length > 0);

const saveStatusText = computed(() => {
    switch (saveStatus.value) {
        case 'saving':
            return 'Saving...';
        case 'saved':
            return 'Saved';
        case 'error':
            return 'Save failed';
        default:
            return '';
    }
});

// Methods
const onTextChange = (event: any) => {
    const newValue = event.htmlValue || '';
    emit('update:modelValue', newValue);

    // Validate content
    const validation = validateScript(newValue, props.actionKey);
    validationResult.value = validation;
    emit('validation-change', validation);

    // Create script config
    const scriptConfig: ScriptConfig = {
        content: newValue,
        variables: validation.variables,
        lastUpdated: new Date(),
        isValid: validation.isValid,
        validationErrors: validation.errors
    };

    emit('script-change', scriptConfig);

    // Auto-save if enabled and valid
    if (props.autoSave?.enabled && tenantId) {
        autoSaveScript(props.actionKey, scriptConfig);
    }
};

const onBlur = () => {
    // Additional validation or actions on blur if needed
};

const insertVariable = (variableKey: string) => {
    const variable = `[${variableKey}]`;
    const currentValue = props.modelValue || '';
    const newValue = currentValue + variable;
    emit('update:modelValue', newValue);
    showHelp.value = false;
};

// Lifecycle
onMounted(() => {
    if (tenantId) {
        onStatusChange(props.actionKey, (status) => {
            saveStatus.value = status;
            emit('save-status-change', status);
        });
    }

    // Initial validation
    if (props.modelValue) {
        const validation = validateScript(props.modelValue, props.actionKey);
        validationResult.value = validation;
        emit('validation-change', validation);
    }
});

onUnmounted(() => {
    if (tenantId) {
        offStatusChange(props.actionKey);
    }
});

// Watch for external model value changes
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            const validation = validateScript(newValue, props.actionKey);
            validationResult.value = validation;
            emit('validation-change', validation);
        }
    }
);
</script>

<style scoped lang="scss">
.script-editor {
    @apply w-full;
}

.action-badge {
    @apply text-sm font-medium;
    min-width: 120px;
    justify-content: center;
}

.editor-container {
    @apply relative;
}

.variables-info {
    @apply p-3 bg-blue-50 border border-blue-200;
}

:deep(.p-editor-toolbar) {
    @apply rounded-t-lg;
}

:deep(.p-editor-content) {
    @apply rounded-b-lg;
}

:deep(.ql-editor) {
    @apply text-sm;
    min-height: 250px;
}

:deep(.ql-editor.ql-blank::before) {
    @apply text-gray-400 italic;
}
</style>
