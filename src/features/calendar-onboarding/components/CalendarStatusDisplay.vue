<template>
    <div class="calendar-status-display">
        <!-- Calendar Creation Status -->
        <Card v-if="calendarId" class="mb-4 calendar-created-card">
            <template #title>
                <div class="flex items-center gap-3">
                    <div class="status-icon success">
                        <i class="pi pi-check"></i>
                    </div>
                    <div>
                        <h3 class="m-0 text-xl font-semibold text-900">Calendar Created</h3>
                        <p class="m-0 text-sm text-600 mt-1">Your Google Calendar has been successfully created</p>
                    </div>
                </div>
            </template>
            <template #content>
                <div class="calendar-info">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="info-item">
                            <div class="label">Calendar Name</div>
                            <div class="value">{{ calendarName || 'N/A' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="label">Calendar ID</div>
                            <div class="value truncate">{{ calendarId }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Validation Status -->
        <Card class="mb-4 validation-card" :class="validationCardClass">
            <template #title>
                <div class="flex items-center gap-3">
                    <div :class="['status-icon', validationStatusClass]">
                        <i :class="validationIconClass"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center gap-2">
                            <h3 class="m-0 text-xl font-semibold text-900">{{ validationTitle }}</h3>
                            <Badge v-if="!error && !isValidated" :value="validationBadgeText" :severity="validationBadgeSeverity" />
                        </div>
                        <p class="m-0 text-sm text-600 mt-1">{{ validationDescription }}</p>
                    </div>
                </div>
            </template>
            <template #content>
                <!-- Progress indicator for validation -->
                <div v-if="isValidating" class="validation-progress">
                    <div class="flex items-center gap-3 mb-3">
                        <ProgressSpinner size="small" />
                        <span class="text-sm font-medium text-600">Setting up integration...</span>
                    </div>
                    <ProgressBar mode="indeterminate" class="mb-2" />
                    <div class="text-xs text-600 text-center">This may take a few moments while we configure your calendar</div>
                </div>
            </template>
        </Card>

        <!-- Integration Status -->
        <Card v-if="isValidated" class="mb-4 success-card">
            <template #title>
                <div class="flex items-center gap-3">
                    <div class="status-icon success">
                        <i class="pi pi-verified"></i>
                    </div>
                    <div>
                        <h3 class="m-0 text-xl font-semibold text-900">Integration Complete!</h3>
                        <p class="m-0 text-sm text-600 mt-1">Your Google Calendar is ready for appointment booking</p>
                    </div>
                </div>
            </template>
            <template #content>
                <div class="integration-features">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="pi pi-sync text-green-600"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">Auto Sync</div>
                                <div class="feature-description">Automatic appointment synchronization</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="pi pi-clock text-blue-600"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">Real-time</div>
                                <div class="feature-description">Live availability checking</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="pi pi-bell text-purple-600"></i>
                            </div>
                            <div class="feature-content">
                                <div class="feature-title">Notifications</div>
                                <div class="feature-description">Customer booking alerts</div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Error Display -->
        <Card v-if="error" class="mb-4 error-card">
            <template #title>
                <div class="flex items-center gap-3">
                    <div class="status-icon error">
                        <i class="pi pi-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h3 class="m-0 text-xl font-semibold text-900">Error Occurred</h3>
                        <p class="m-0 text-sm text-600 mt-1">There was an issue with the calendar setup</p>
                    </div>
                </div>
            </template>
            <template #content>
                <Message severity="error" :closable="false" class="mb-0">
                    {{ error }}
                </Message>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Card from 'primevue/card';
import Badge from 'primevue/badge';
import ProgressBar from 'primevue/progressbar';
import ProgressSpinner from 'primevue/progressspinner';
import Message from 'primevue/message';

// Props
interface Props {
    calendarId?: string | null;
    calendarName?: string;
    isValidated?: boolean;
    isValidating?: boolean;
    error?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
    calendarId: null,
    calendarName: '',
    isValidated: false,
    isValidating: false,
    error: null
});

// Computed
const validationStatusClass = computed(() => {
    if (props.error) return 'error';
    if (props.isValidated) return 'success';
    if (props.isValidating) return 'warning';
    return 'pending';
});

const validationIconClass = computed(() => {
    if (props.error) return 'pi pi-times';
    if (props.isValidated) return 'pi pi-check';
    if (props.isValidating) return 'pi pi-spin pi-spinner';
    return 'pi pi-clock';
});

const validationTitle = computed(() => {
    if (props.error) return 'Validation Failed';
    if (props.isValidated) return 'Calendar Validated';
    if (props.isValidating) return 'Validating Calendar';
    return 'Waiting for Validation';
});

const validationDescription = computed(() => {
    if (props.error) return 'There was an error validating your calendar';
    if (props.isValidated) return 'Your calendar has been successfully validated and integrated';
    if (props.isValidating) return 'We are setting up your calendar integration';
    return 'Calendar validation will begin automatically';
});

const validationCardClass = computed(() => {
    if (props.error) return 'border-red-200 bg-red-50';
    if (props.isValidated) return 'border-green-200 bg-green-50';
    if (props.isValidating) return 'border-yellow-200 bg-yellow-50';
    return 'border-blue-200 bg-blue-50';
});

const validationBadgeText = computed(() => {
    if (props.isValidating) return 'In Progress';
    return 'Pending';
});

const validationBadgeSeverity = computed(() => {
    if (props.isValidating) return 'warn';
    return 'info';
});
</script>

<style scoped>
.calendar-status-display {
}

/* Card Styling */
:deep(.p-card) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--surface-border);
    transition: all 0.3s ease;
}

:deep(.p-card:hover) {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

:deep(.p-card-title) {
    padding: 1.5rem 1.5rem 0 1.5rem;
    margin-bottom: 0;
}

:deep(.p-card-content) {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

.calendar-created-card :deep(.p-card) {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: var(--green-200);
}

.success-card :deep(.p-card) {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-color: var(--green-300);
}

.error-card :deep(.p-card) {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-color: var(--red-300);
}

/* Status Icons */
.status-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-icon.success {
    background: linear-gradient(135deg, var(--green-100), var(--green-200));
    color: var(--green-700);
    border: 2px solid var(--green-300);
}

.status-icon.warning {
    background: linear-gradient(135deg, var(--yellow-100), var(--yellow-200));
    color: var(--yellow-700);
    border: 2px solid var(--yellow-300);
}

.status-icon.error {
    background: linear-gradient(135deg, var(--red-100), var(--red-200));
    color: var(--red-700);
    border: 2px solid var(--red-300);
}

.status-icon.pending {
    background: linear-gradient(135deg, var(--blue-100), var(--blue-200));
    color: var(--blue-700);
    border: 2px solid var(--blue-300);
}

/* Calendar Info */
.calendar-info {
    background: var(--surface-50);
    border-radius: 8px;
    padding: 1.25rem;
    border: 1px solid var(--surface-200);
}

.info-item {
    border-radius: 6px;
    padding: 1rem;
    border: 1px solid var(--surface-200);
    transition: all 0.2s ease;
}

.info-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.label {
    font-weight: 600;
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.value {
    color: var(--text-color);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    font-weight: 500;
    word-break: break-all;
}

/* Validation Progress */
.validation-progress {
    background: var(--surface-50);
    border-radius: 8px;
    padding: 1.25rem;
    border: 1px solid var(--surface-200);
}

/* Integration Features */
.integration-features {
    background: var(--surface-50);
    border-radius: 8px;
    padding: 1.25rem;
    border: 1px solid var(--surface-200);
}

.feature-item {
    border-radius: 8px;
    padding: 1.25rem;
    border: 1px solid var(--surface-200);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin: 0 auto 1rem auto;
    background: var(--surface-100);
    border: 2px solid var(--surface-200);
}

.feature-content {
    text-align: center;
}

.feature-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.feature-description {
    color: var(--text-color-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Responsive */
@media (max-width: 768px) {
    .calendar-status-display {
        max-width: 100%;
    }

    .status-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
    }

    .integration-features .grid {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.text-green-600 {
    color: var(--green-600);
}

.text-blue-600 {
    color: var(--blue-600);
}

.text-purple-600 {
    color: var(--purple-600);
}

.text-900 {
    color: var(--text-color);
}

.text-600 {
    color: var(--text-color-secondary);
}
</style>
