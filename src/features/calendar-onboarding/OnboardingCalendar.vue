<template>
    <OnboardingLayout>
        <!-- Header -->
        <template #header>
            <OnboardingHeader title="Google Calendar Integration" description="Connect your Google Calendar to enable seamless appointment booking and management for your business." icon="pi pi-google" />
        </template>

        <!-- Steps -->
        <template #steps>
            <OnboardingSteps :steps="formattedSteps" :current-step="service.state.value.currentStep" :allow-step-navigation="false" />
        </template>

        <!-- Main Content -->
        <OnboardingCard variant="default" size="large" :loading="service.state.value.isLoading">
            <!-- Step 1: Setup Calendar -->
            <div v-if="service.state.value.currentStep === 0" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-calendar-plus" size="large" variant="primary" class="mb-4" />
                    <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Create Your Calendar</h2>
                    <p class="text-surface-600 dark:text-surface-400">Enter a name for your business calendar. This will be used for all your appointment bookings.</p>
                </div>

                <CalendarNameInput
                    :calendar-name="service.state.value.formData.calendarName"
                    :timezone="service.state.value.formData.timezone"
                    :error="service.state.value.formData.error"
                    :disabled="service.state.value.isLoading"
                    @update:calendar-name="handleCalendarNameUpdate"
                    @update:timezone="handleTimezoneUpdate"
                />

                <div class="flex justify-center">
                    <Button label="Create Calendar" icon="pi pi-plus" size="large" :loading="service.state.value.isLoading" :disabled="!service.isFormValid.value || service.state.value.isLoading" @click="handleCreateCalendar" class="px-8" />
                </div>
            </div>

            <!-- Step 2: Validation -->
            <div v-else-if="service.state.value.currentStep === 1" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-sync" size="large" variant="info" class="mb-4" />
                    <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-2">Setting Up Integration</h2>
                    <p class="text-surface-600 dark:text-surface-400">We're configuring your Google Calendar integration. This process is automatic and may take a few moments.</p>
                </div>

                <CalendarStatusDisplay
                    :calendar-id="service.state.value.calendarId"
                    :calendar-name="service.state.value.formData.calendarName"
                    :is-validated="service.state.value.isValidated"
                    :is-validating="!service.state.value.isValidated && !!service.state.value.calendarId"
                    :error="service.state.value.error"
                />
            </div>

            <!-- Step 3: Complete -->
            <div v-else-if="service.state.value.currentStep === 2" class="space-y-6">
                <div class="text-center">
                    <FeatureIcon icon="pi pi-check-circle" size="xl" variant="success" class="mb-4" />
                    <h2 class="text-2xl font-semibold text-green-700 dark:text-green-400 mb-2">Integration Complete!</h2>
                    <p class="text-surface-600 dark:text-surface-400">Your Google Calendar is now connected and ready for appointment bookings.</p>
                </div>

                <CalendarStatusDisplay
                    :calendar-id="service.state.value.calendarId"
                    :calendar-name="service.state.value.formData.calendarName"
                    :is-validated="service.state.value.isValidated"
                    :is-validating="false"
                    :error="service.state.value.error"
                />

                <div class="flex gap-3 justify-center flex-wrap">
                    <Button label="View Calendar" icon="pi pi-external-link" severity="secondary" outlined @click="openGoogleCalendar" />
                    <Button label="Start Booking Appointments" icon="pi pi-calendar-plus" @click="navigateToAppointments" />
                </div>
            </div>

            <!-- Error State -->
            <div v-if="service.state.value.error && service.state.value.currentStep === 0" class="mt-6">
                <Message severity="error" :closable="false">
                    <div class="flex items-center">
                        <i class="pi pi-exclamation-triangle mr-2"></i>
                        <div>
                            <strong>Error:</strong> {{ service.state.value.error }}
                            <div class="mt-2">
                                <Button label="Try Again" icon="pi pi-refresh" size="small" @click="handleRetry" />
                            </div>
                        </div>
                    </div>
                </Message>
            </div>
        </OnboardingCard>

        <!-- Help Section -->
        <template #footer>
            <OnboardingCard variant="outlined" size="small" centered>
                <div class="text-center space-y-3">
                    <FeatureIcon icon="pi pi-question-circle" size="medium" variant="info" />
                    <div>
                        <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-2">Need Help?</h3>
                        <p class="text-surface-600 dark:text-surface-400 mb-4">If you're experiencing issues with the calendar integration, please contact our support team.</p>
                        <Button label="Contact Support" icon="pi pi-question-circle" severity="secondary" text @click="contactSupport" />
                    </div>
                </div>
            </OnboardingCard>
        </template>
    </OnboardingLayout>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';

// Shared components
import { OnboardingLayout, OnboardingHeader, OnboardingSteps, OnboardingCard, FeatureIcon, type OnboardingStep } from '@/shared/components/onboarding';

// Feature components
import { useCalendarOnboardingService } from './services/calendarOnboardingService';
import CalendarNameInput from './components/CalendarNameInput.vue';
import CalendarStatusDisplay from './components/CalendarStatusDisplay.vue';
import { SUCCESS_MESSAGES } from './types';

// Composables
const service = useCalendarOnboardingService();
const router = useRouter();
const toast = useToast();

// Computed
const formattedSteps = computed<OnboardingStep[]>(() => [
    {
        id: 'setup',
        title: 'Setup',
        description: 'Create calendar',
        icon: 'pi pi-calendar-plus',
        completed: service.state.value.currentStep > 0
    },
    {
        id: 'validation',
        title: 'Validation',
        description: 'Verify integration',
        icon: 'pi pi-sync',
        completed: service.state.value.currentStep > 1
    },
    {
        id: 'complete',
        title: 'Complete',
        description: 'Ready to use',
        icon: 'pi pi-check',
        completed: service.state.value.currentStep > 2
    }
]);

// Emits
interface Emits {
    (e: 'complete', data: { calendarId: string; calendarName: string }): void;
}

const emit = defineEmits<Emits>();

// Methods
const handleCalendarNameUpdate = (value: string) => {
    service.updateFormData({ calendarName: value });
};

const handleTimezoneUpdate = (value: string) => {
    service.updateFormData({ timezone: value });
};

const handleCreateCalendar = async () => {
    const result = await service.createCalendar();

    if (result.success) {
        toast.add({
            severity: 'success',
            summary: 'Success',
            detail: SUCCESS_MESSAGES.CALENDAR_CREATED,
            life: 5000
        });
    } else {
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: result.error || 'Failed to create calendar',
            life: 5000
        });
    }
};

const handleRetry = () => {
    service.setError(null);
};

const openGoogleCalendar = () => {
    if (service.state.value.calendarId) {
        const url = `https://calendar.google.com/calendar/embed?src=${service.state.value.calendarId}`;
        window.open(url, '_blank');
    }
};

const navigateToAppointments = () => {
    router.push('/appointments');
};

const contactSupport = () => {
    // Implement support contact logic
    window.open('mailto:<EMAIL>?subject=Calendar Integration Help', '_blank');
};

// Watch for completion
watch(
    () => service.state.value.currentStep,
    (newStep) => {
        if (newStep === 2 && service.state.value.isValidated && service.state.value.calendarId) {
            // Emit complete event when prompts-onboarding is finished
            // emit('complete', {
            //     calendarId: service.state.value.calendarId,
            //     calendarName: service.state.value.formData.calendarName
            // });
        }
    }
);

// Lifecycle
onMounted(async () => {
    await service.initialize();
});

onUnmounted(() => {
    service.cleanup();
});
</script>

<style scoped>
/* Custom styles for calendar onboarding specific elements */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Responsive button adjustments */
@media (max-width: 640px) {
    .flex.gap-3 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .flex.gap-3 .p-button {
        width: 100%;
    }
}
</style>
