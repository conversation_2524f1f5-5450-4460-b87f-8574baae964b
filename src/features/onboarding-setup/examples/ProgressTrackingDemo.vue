<template>
    <div class="progress-demo">
        <Card>
            <template #header>
                <div class="p-4">
                    <h2 class="text-xl font-bold">Progress Tracking Demo</h2>
                    <p class="text-surface-600 dark:text-surface-400">
                        Demonstrates the compact and full view modes of the progress tracking component
                    </p>
                </div>
            </template>
            <template #content>
                <!-- Demo Controls -->
                <div class="demo-controls mb-6">
                    <div class="flex flex-wrap gap-3">
                        <Button
                            label="Simulate Progress"
                            icon="pi pi-play"
                            @click="simulateProgress"
                            :disabled="isSimulating"
                        />
                        <Button
                            label="Reset Progress"
                            icon="pi pi-refresh"
                            severity="secondary"
                            outlined
                            @click="resetProgress"
                        />
                        <Button
                            label="Complete Random Feature"
                            icon="pi pi-check"
                            severity="success"
                            outlined
                            @click="completeRandomFeature"
                        />
                        <Button
                            label="Skip Random Feature"
                            icon="pi pi-arrow-right"
                            severity="warning"
                            outlined
                            @click="skipRandomFeature"
                        />
                    </div>
                </div>

                <!-- Progress Tracking Component -->
                <div class="progress-demo-container">
                    <div class="demo-section">
                        <h3 class="text-lg font-semibold mb-3">Compact Progress View</h3>
                        <p class="text-sm text-surface-600 dark:text-surface-400 mb-4">
                            This is the default compact view that doesn't overwhelm the main content. 
                            Click the eye icon to open the detailed view in a modal.
                        </p>
                        
                        <div class="progress-container">
                            <ProgressTracking
                                :progress="mockProgress"
                                :current-feature="currentFeature"
                                :features="mockFeatures"
                                @step-click="handleStepClick"
                                @toggle-view="handleToggleView"
                            />
                        </div>
                    </div>

                    <!-- Feature Container Simulation -->
                    <div class="demo-section mt-6">
                        <h3 class="text-lg font-semibold mb-3">Feature Container (Simulated)</h3>
                        <p class="text-sm text-surface-600 dark:text-surface-400 mb-4">
                            This represents the main feature content area. Notice how the compact progress 
                            tracking doesn't take up too much space, leaving room for the actual feature setup.
                        </p>
                        
                        <Card class="feature-simulation">
                            <template #content>
                                <div class="text-center py-8">
                                    <div v-if="currentFeature" class="current-feature-display">
                                        <i :class="currentFeature.icon" class="text-4xl text-primary mb-3"></i>
                                        <h4 class="text-xl font-semibold mb-2">{{ currentFeature.title }}</h4>
                                        <p class="text-surface-600 dark:text-surface-400 mb-4">
                                            {{ currentFeature.description }}
                                        </p>
                                        <div class="flex justify-center gap-3">
                                            <Button
                                                label="Complete Setup"
                                                icon="pi pi-check"
                                                @click="completeCurrentFeature"
                                            />
                                            <Button
                                                v-if="!currentFeature.isRequired"
                                                label="Skip"
                                                icon="pi pi-arrow-right"
                                                severity="secondary"
                                                outlined
                                                @click="skipCurrentFeature"
                                            />
                                        </div>
                                    </div>
                                    <div v-else class="no-feature">
                                        <i class="pi pi-check-circle text-6xl text-green-500 mb-3"></i>
                                        <h4 class="text-xl font-semibold mb-2">All Features Complete!</h4>
                                        <p class="text-surface-600 dark:text-surface-400">
                                            Great job! You've completed all the onboarding features.
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </div>
                </div>

                <!-- Event Log -->
                <div v-if="eventLog.length > 0" class="demo-section mt-6">
                    <h3 class="text-lg font-semibold mb-3">Event Log</h3>
                    <div class="space-y-2 max-h-40 overflow-y-auto">
                        <div
                            v-for="(event, index) in eventLog"
                            :key="index"
                            class="p-3 bg-surface-50 dark:bg-surface-800 rounded border-l-4 border-primary-400"
                        >
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="font-medium">{{ event.message }}</div>
                                    <div class="text-sm text-surface-600 dark:text-surface-400">
                                        {{ event.details }}
                                    </div>
                                </div>
                                <div class="text-xs text-surface-500">
                                    {{ formatTime(event.timestamp) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useToast } from 'primevue/usetoast';

// PrimeVue Components
import Card from 'primevue/card';
import Button from 'primevue/button';

// Progress Tracking Component
import ProgressTracking from '../components/ProgressTracking.vue';

// Constants and Types
import { ONBOARDING_FEATURES, FEATURE_ORDER } from '../constants';
import type { OnboardingFeature, OnboardingProgress } from '../types';

// Composables
const toast = useToast();

// State
const isSimulating = ref(false);
const eventLog = ref<Array<{
    message: string;
    details: string;
    timestamp: Date;
}>>([]);

// Mock data
const mockFeatures = ref<OnboardingFeature[]>(
    FEATURE_ORDER.map((featureId, index) => {
        const config = ONBOARDING_FEATURES[featureId];
        return {
            id: featureId,
            name: featureId,
            title: config.title,
            description: config.description,
            icon: config.icon,
            color: config.color,
            category: config.category as any,
            priority: index,
            estimatedTime: config.estimatedTime,
            dependencies: [],
            component: featureId,
            isRequired: config.isRequired,
            isCompleted: false,
            isSkipped: false
        };
    })
);

const mockProgress = ref<OnboardingProgress>({
    totalFeatures: mockFeatures.value.length,
    completedFeatures: 0,
    skippedFeatures: 0,
    progressPercentage: 0,
    estimatedTimeRemaining: '65 minutes',
    startedAt: new Date(),
    lastUpdatedAt: new Date()
});

// Computed
const currentFeature = computed(() => {
    return mockFeatures.value.find(f => !f.isCompleted && !f.isSkipped) || null;
});

// Methods
const updateProgress = () => {
    const completed = mockFeatures.value.filter(f => f.isCompleted).length;
    const skipped = mockFeatures.value.filter(f => f.isSkipped).length;
    const total = mockFeatures.value.length;
    
    mockProgress.value = {
        ...mockProgress.value,
        completedFeatures: completed,
        skippedFeatures: skipped,
        progressPercentage: Math.round(((completed + skipped) / total) * 100),
        estimatedTimeRemaining: calculateTimeRemaining(),
        lastUpdatedAt: new Date()
    };
};

const calculateTimeRemaining = () => {
    const remaining = mockFeatures.value.filter(f => !f.isCompleted && !f.isSkipped);
    const totalMinutes = remaining.reduce((total, feature) => {
        const minutes = parseInt(feature.estimatedTime.match(/\d+/)?.[0] || '0');
        return total + minutes;
    }, 0);
    
    if (totalMinutes === 0) return '0 minutes';
    if (totalMinutes < 60) return `${totalMinutes} minutes`;
    
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
};

const addEvent = (message: string, details: string) => {
    eventLog.value.unshift({
        message,
        details,
        timestamp: new Date()
    });
    
    if (eventLog.value.length > 10) {
        eventLog.value = eventLog.value.slice(0, 10);
    }
};

const simulateProgress = async () => {
    isSimulating.value = true;
    addEvent('Simulation Started', 'Automatically completing features every 2 seconds');
    
    for (let i = 0; i < mockFeatures.value.length && isSimulating.value; i++) {
        const feature = mockFeatures.value[i];
        if (!feature.isCompleted && !feature.isSkipped) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (Math.random() > 0.8 && !feature.isRequired) {
                feature.isSkipped = true;
                addEvent('Feature Skipped', `${feature.title} was skipped during simulation`);
            } else {
                feature.isCompleted = true;
                feature.completedAt = new Date();
                addEvent('Feature Completed', `${feature.title} was completed during simulation`);
            }
            
            updateProgress();
        }
    }
    
    isSimulating.value = false;
    addEvent('Simulation Complete', 'All features have been processed');
};

const resetProgress = () => {
    mockFeatures.value.forEach(feature => {
        feature.isCompleted = false;
        feature.isSkipped = false;
        feature.completedAt = undefined;
    });
    updateProgress();
    addEvent('Progress Reset', 'All features reset to initial state');
};

const completeRandomFeature = () => {
    const pending = mockFeatures.value.filter(f => !f.isCompleted && !f.isSkipped);
    if (pending.length > 0) {
        const randomFeature = pending[Math.floor(Math.random() * pending.length)];
        randomFeature.isCompleted = true;
        randomFeature.completedAt = new Date();
        updateProgress();
        addEvent('Random Feature Completed', `${randomFeature.title} was completed`);
    }
};

const skipRandomFeature = () => {
    const pending = mockFeatures.value.filter(f => !f.isCompleted && !f.isSkipped && !f.isRequired);
    if (pending.length > 0) {
        const randomFeature = pending[Math.floor(Math.random() * pending.length)];
        randomFeature.isSkipped = true;
        updateProgress();
        addEvent('Random Feature Skipped', `${randomFeature.title} was skipped`);
    }
};

const completeCurrentFeature = () => {
    if (currentFeature.value) {
        currentFeature.value.isCompleted = true;
        currentFeature.value.completedAt = new Date();
        updateProgress();
        addEvent('Current Feature Completed', `${currentFeature.value.title} setup completed`);
        
        toast.add({
            severity: 'success',
            summary: 'Feature Complete',
            detail: `${currentFeature.value.title} has been set up successfully`,
            life: 3000
        });
    }
};

const skipCurrentFeature = () => {
    if (currentFeature.value && !currentFeature.value.isRequired) {
        currentFeature.value.isSkipped = true;
        updateProgress();
        addEvent('Current Feature Skipped', `${currentFeature.value.title} was skipped`);
        
        toast.add({
            severity: 'info',
            summary: 'Feature Skipped',
            detail: `${currentFeature.value.title} has been skipped`,
            life: 3000
        });
    }
};

const handleStepClick = (feature: OnboardingFeature) => {
    addEvent('Step Clicked', `User clicked on ${feature.title} in progress tracking`);
    
    toast.add({
        severity: 'info',
        summary: 'Step Clicked',
        detail: `Navigating to ${feature.title}`,
        life: 3000
    });
};

const handleToggleView = (isFullView: boolean) => {
    addEvent('View Toggled', `Progress view changed to ${isFullView ? 'Full View' : 'Compact View'}`);
};

const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// Lifecycle
onMounted(() => {
    updateProgress();
    addEvent('Demo Initialized', 'Progress tracking demo is ready');
});
</script>

<style scoped>
.progress-demo {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.demo-controls {
    background: var(--surface-50);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--surface-border);
}

.dark .demo-controls {
    background: var(--surface-800);
    border-color: var(--surface-700);
}

.progress-container {
    background: var(--surface-0);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid var(--surface-border);
}

.dark .progress-container {
    background: var(--surface-900);
    border-color: var(--surface-700);
}

.feature-simulation {
    min-height: 300px;
    display: flex;
    align-items: center;
}

.current-feature-display,
.no-feature {
    width: 100%;
}

.demo-section {
    margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .progress-demo {
        padding: 1rem 0.5rem;
    }
    
    .demo-controls .flex {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
