<template>
    <div class="onboarding-dashboard">
        <!-- Dashboard Header -->
        <div class="dashboard-header mb-6">
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                        Setup Dashboard
                    </h1>
                    <p class="text-surface-600 dark:text-surface-400">
                        Track your onboarding progress and manage feature configurations
                    </p>
                </div>
                <div class="flex gap-3">
                    <Button
                        label="Continue Setup"
                        icon="pi pi-play"
                        @click="continueOnboarding"
                        :disabled="isCompleted"
                    />
                    <Button
                        label="Add Features"
                        icon="pi pi-plus"
                        severity="secondary"
                        outlined
                        @click="addMoreFeatures"
                    />
                </div>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="progress-overview mb-6">
            <Card>
                <template #content>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <!-- Overall Progress -->
                        <div class="progress-stat">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                                    Overall Progress
                                </h3>
                                <div class="text-2xl font-bold text-primary">
                                    {{ overallProgress }}%
                                </div>
                            </div>
                            <ProgressBar :value="overallProgress" class="mb-2" />
                            <div class="text-sm text-surface-600 dark:text-surface-400">
                                {{ completedCount }} of {{ totalCount }} features completed
                            </div>
                        </div>

                        <!-- Time Stats -->
                        <div class="time-stats">
                            <div class="stat-item">
                                <div class="stat-value text-green-500">{{ timeSpent }}</div>
                                <div class="stat-label">Time Spent</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-blue-500">{{ timeRemaining }}</div>
                                <div class="stat-label">Time Remaining</div>
                            </div>
                        </div>

                        <!-- Feature Stats -->
                        <div class="feature-stats">
                            <div class="stat-item">
                                <div class="stat-value text-green-500">{{ completedCount }}</div>
                                <div class="stat-label">Completed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-orange-500">{{ skippedCount }}</div>
                                <div class="stat-label">Skipped</div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <div class="space-y-2">
                                <Button
                                    v-if="!isCompleted"
                                    label="Resume"
                                    icon="pi pi-play"
                                    size="small"
                                    class="w-full"
                                    @click="continueOnboarding"
                                />
                                <Button
                                    label="Export Report"
                                    icon="pi pi-download"
                                    severity="secondary"
                                    outlined
                                    size="small"
                                    class="w-full"
                                    @click="exportReport"
                                />
                            </div>
                        </div>
                    </div>
                </template>
            </Card>
        </div>

        <!-- Feature Categories -->
        <div class="feature-categories">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div v-for="category in categories" :key="category.id" class="category-card">
                    <Card>
                        <template #header>
                            <div class="category-header">
                                <div class="flex items-center gap-3">
                                    <i :class="[category.icon, category.color]" class="text-2xl"></i>
                                    <div>
                                        <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                                            {{ category.title }}
                                        </h3>
                                        <p class="text-sm text-surface-600 dark:text-surface-400">
                                            {{ category.description }}
                                        </p>
                                    </div>
                                </div>
                                <div class="category-progress">
                                    <div class="text-sm font-medium text-surface-700 dark:text-surface-300">
                                        {{ getCategoryProgress(category.id) }}%
                                    </div>
                                    <ProgressBar
                                        :value="getCategoryProgress(category.id)"
                                        class="category-progress-bar"
                                        :show-value="false"
                                    />
                                </div>
                            </div>
                        </template>
                        <template #content>
                            <div class="space-y-3">
                                <div
                                    v-for="feature in getCategoryFeatures(category.id)"
                                    :key="feature.id"
                                    class="feature-item"
                                    :class="getFeatureClass(feature)"
                                    @click="handleFeatureClick(feature)"
                                >
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="feature-status-icon">
                                                <i v-if="feature.isCompleted" class="pi pi-check-circle text-green-500"></i>
                                                <i v-else-if="feature.isSkipped" class="pi pi-arrow-circle-right text-orange-500"></i>
                                                <i v-else class="pi pi-clock text-surface-400"></i>
                                            </div>
                                            <div>
                                                <div class="font-medium text-surface-900 dark:text-surface-0">
                                                    {{ feature.title }}
                                                </div>
                                                <div class="text-sm text-surface-600 dark:text-surface-400">
                                                    {{ feature.estimatedTime }}
                                                    <Tag
                                                        v-if="feature.isRequired"
                                                        value="Required"
                                                        severity="warning"
                                                        class="ml-2 text-xs"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="feature-actions">
                                            <Button
                                                v-if="!feature.isCompleted && !feature.isSkipped"
                                                icon="pi pi-play"
                                                severity="secondary"
                                                text
                                                size="small"
                                                @click.stop="startFeature(feature)"
                                                v-tooltip="'Start setup'"
                                            />
                                            <Button
                                                v-else-if="feature.isCompleted"
                                                icon="pi pi-cog"
                                                severity="secondary"
                                                text
                                                size="small"
                                                @click.stop="configureFeature(feature)"
                                                v-tooltip="'Reconfigure'"
                                            />
                                            <Button
                                                v-else
                                                icon="pi pi-refresh"
                                                severity="secondary"
                                                text
                                                size="small"
                                                @click.stop="retryFeature(feature)"
                                                v-tooltip="'Retry setup'"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </Card>
                </div>
            </div>
        </div>

        <!-- Completion Banner -->
        <div v-if="isCompleted" class="completion-banner mt-6">
            <Card class="completion-card">
                <template #content>
                    <div class="text-center py-6">
                        <div class="completion-icon mb-4">
                            <i class="pi pi-check-circle text-6xl text-green-500"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">
                            🎉 Setup Complete!
                        </h2>
                        <p class="text-surface-600 dark:text-surface-400 mb-6">
                            Congratulations! You've successfully configured your SaaS platform.
                            You're now ready to start managing leads and growing your business.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <Button
                                label="Go to Dashboard"
                                icon="pi pi-home"
                                @click="goToDashboard"
                            />
                            <Button
                                label="View Documentation"
                                icon="pi pi-book"
                                severity="secondary"
                                outlined
                                @click="viewDocumentation"
                            />
                        </div>
                    </div>
                </template>
            </Card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// PrimeVue Components
import Card from 'primevue/card';
import Button from 'primevue/button';
import ProgressBar from 'primevue/progressbar';
import Tag from 'primevue/tag';

// Types
import type { OnboardingFeature, FeatureCategory } from '../types';
import { FEATURE_CATEGORIES } from '../constants';

// Props
interface Props {
    features: OnboardingFeature[];
    startedAt?: Date;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
    'continue-onboarding': [];
    'add-features': [];
    'start-feature': [feature: OnboardingFeature];
    'configure-feature': [feature: OnboardingFeature];
    'retry-feature': [feature: OnboardingFeature];
    'export-report': [];
    'go-to-dashboard': [];
}>();

// Computed
const categories = computed(() => Object.values(FEATURE_CATEGORIES));

const totalCount = computed(() => props.features.length);
const completedCount = computed(() => props.features.filter(f => f.isCompleted).length);
const skippedCount = computed(() => props.features.filter(f => f.isSkipped).length);

const overallProgress = computed(() => {
    if (totalCount.value === 0) return 0;
    return Math.round(((completedCount.value + skippedCount.value) / totalCount.value) * 100);
});

const isCompleted = computed(() => {
    return props.features.every(f => f.isCompleted || f.isSkipped);
});

const timeSpent = computed(() => {
    if (!props.startedAt) return '0m';
    const minutes = Math.round((Date.now() - props.startedAt.getTime()) / 60000);
    return minutes < 60 ? `${minutes}m` : `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
});

const timeRemaining = computed(() => {
    const remainingFeatures = props.features.filter(f => !f.isCompleted && !f.isSkipped);
    const totalMinutes = remainingFeatures.reduce((total, feature) => {
        const minutes = parseInt(feature.estimatedTime.match(/\d+/)?.[0] || '0');
        return total + minutes;
    }, 0);

    if (totalMinutes === 0) return '0m';
    return totalMinutes < 60 ? `${totalMinutes}m` : `${Math.floor(totalMinutes / 60)}h ${totalMinutes % 60}m`;
});

// Methods
const getCategoryFeatures = (categoryId: string) => {
    return props.features.filter(f => f.category === categoryId);
};

const getCategoryProgress = (categoryId: string): number => {
    const categoryFeatures = getCategoryFeatures(categoryId);
    if (categoryFeatures.length === 0) return 100;

    const completed = categoryFeatures.filter(f => f.isCompleted || f.isSkipped).length;
    return Math.round((completed / categoryFeatures.length) * 100);
};

const getFeatureClass = (feature: OnboardingFeature) => {
    const classes = ['cursor-pointer'];
    if (feature.isCompleted) classes.push('completed');
    else if (feature.isSkipped) classes.push('skipped');
    else classes.push('pending');
    return classes.join(' ');
};

const handleFeatureClick = (feature: OnboardingFeature) => {
    if (feature.isCompleted) {
        configureFeature(feature);
    } else if (feature.isSkipped) {
        retryFeature(feature);
    } else {
        startFeature(feature);
    }
};

const continueOnboarding = () => {
    emit('continue-onboarding');
};

const addMoreFeatures = () => {
    emit('add-features');
};

const startFeature = (feature: OnboardingFeature) => {
    emit('start-feature', feature);
};

const configureFeature = (feature: OnboardingFeature) => {
    emit('configure-feature', feature);
};

const retryFeature = (feature: OnboardingFeature) => {
    emit('retry-feature', feature);
};

const exportReport = () => {
    emit('export-report');
};

const goToDashboard = () => {
    emit('go-to-dashboard');
};

const viewDocumentation = () => {
    // Open documentation in new tab
    window.open('/docs', '_blank');
};
</script>

<style scoped>
.onboarding-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Dashboard Header */
.dashboard-header {
    margin-bottom: 2rem;
}

/* Progress Overview */
.progress-overview :deep(.p-card-content) {
    padding: 2rem;
}

.progress-stat {
    text-align: center;
}

.time-stats,
.feature-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--surface-600);
}

.dark .stat-label {
    color: var(--surface-400);
}

.quick-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Feature Categories */
.category-card :deep(.p-card-header) {
    padding: 1.5rem 1.5rem 0 1.5rem;
}

.category-card :deep(.p-card-content) {
    padding: 0 1.5rem 1.5rem 1.5rem;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.category-progress {
    text-align: right;
    min-width: 100px;
}

.category-progress-bar {
    margin-top: 0.5rem;
}

.category-progress-bar :deep(.p-progressbar) {
    height: 0.5rem;
}

/* Feature Items */
.feature-item {
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.feature-item:hover {
    background: var(--surface-50);
    border-color: var(--surface-200);
}

.dark .feature-item:hover {
    background: var(--surface-800);
    border-color: var(--surface-700);
}

.feature-item.completed {
    background: var(--green-50);
    border-color: var(--green-200);
}

.dark .feature-item.completed {
    background: var(--green-900);
    border-color: var(--green-700);
}

.feature-item.skipped {
    background: var(--orange-50);
    border-color: var(--orange-200);
}

.dark .feature-item.skipped {
    background: var(--orange-900);
    border-color: var(--orange-700);
}

.feature-item.pending {
    background: var(--surface-0);
    border-color: var(--surface-200);
}

.dark .feature-item.pending {
    background: var(--surface-900);
    border-color: var(--surface-700);
}

.feature-status-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-actions {
    display: flex;
    gap: 0.5rem;
}

/* Completion Banner */
.completion-banner {
    margin-top: 3rem;
}

.completion-card {
    background: linear-gradient(135deg, var(--green-50) 0%, var(--primary-50) 100%);
    border: 2px solid var(--green-200);
}

.dark .completion-card {
    background: linear-gradient(135deg, var(--green-900) 0%, var(--primary-900) 100%);
    border-color: var(--green-700);
}

.completion-icon {
    animation: completionPulse 2s infinite;
}

@keyframes completionPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .onboarding-dashboard {
        padding: 1rem 0.5rem;
    }

    .dashboard-header .flex {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .progress-overview :deep(.p-card-content) {
        padding: 1rem;
    }

    .progress-overview .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .time-stats,
    .feature-stats {
        flex-direction: row;
        justify-content: space-around;
    }

    .category-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .category-progress {
        text-align: left;
    }

    .feature-item .flex {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .feature-actions {
        justify-content: center;
    }
}

/* Animation for feature interactions */
.feature-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-item:active {
    transform: scale(0.98);
}

/* Progress bar animations */
.progress-overview :deep(.p-progressbar-value) {
    transition: width 1s ease-in-out;
}

.category-progress-bar :deep(.p-progressbar-value) {
    transition: width 0.8s ease-in-out;
}
</style>
