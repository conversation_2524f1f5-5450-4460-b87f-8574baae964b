<template>
    <div class="chatbot-prompt-editor">
        <!-- Header Section -->
        <div class="mb-6">
            <div class="flex items-center gap-3 mb-3">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <i class="pi pi-comments text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0">Chatbot Prompt Configuration</h3>
                    <p class="text-sm text-surface-600 dark:text-surface-400">Configure how your AI chatbot responds to customer inquiries</p>
                </div>
            </div>

            <!-- Info Panel -->
            <Card class="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
                <template #content>
                    <div class="flex items-start gap-3">
                        <i class="pi pi-info-circle text-blue-600 dark:text-blue-400 mt-1"></i>
                        <div>
                            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">What is the Chatbot Prompt?</h4>
                            <p class="text-sm text-blue-800 dark:text-blue-200 mb-2">
                                This prompt defines your AI chatbot's personality, knowledge, and response style when interacting with customers through various channels (email, SMS, social media).
                            </p>
                            <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                                <li>• Sets the tone and personality of your AI assistant</li>
                                <li>• Defines what information the AI can provide about your business</li>
                                <li>• Controls how the AI handles customer inquiries and lead qualification</li>
                                <li>• Ensures consistent brand voice across all customer interactions</li>
                            </ul>
                        </div>
                    </div>
                </template>
            </Card>
        </div>

        <!-- Prompt Content Section -->
        <div class="mb-6">
            <label for="chatbot-prompt" class="block text-lg font-medium text-surface-900 dark:text-surface-0 mb-3">
                Chatbot Instructions
                <span class="text-red-500 ml-1">*</span>
            </label>

            <Editor
                id="chatbot-prompt"
                v-model="localPrompt"
                :placeholder="promptPlaceholder"
                :readonly="disabled || isLoading"
                editorStyle="height: 300px"
                class="w-full"
                :class="{ 'p-invalid': hasError }"
                @text-change="onPromptChange"
                @blur="onPromptBlur"
            >
                <template #toolbar>
                    <span class="ql-formats">
                        <button class="ql-bold" v-tooltip.bottom="'Bold'"></button>
                        <button class="ql-italic" v-tooltip.bottom="'Italic'"></button>
                        <button class="ql-underline" v-tooltip.bottom="'Underline'"></button>
                        <button class="ql-strike" v-tooltip.bottom="'Strikethrough'"></button>
                    </span>
                    <span class="ql-formats">
                        <select class="ql-header" v-tooltip.bottom="'Header'">
                            <option value="1">Heading 1</option>
                            <option value="2">Heading 2</option>
                            <option value="3">Heading 3</option>
                            <option selected>Normal</option>
                        </select>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-list" value="ordered" v-tooltip.bottom="'Numbered List'"></button>
                        <button class="ql-list" value="bullet" v-tooltip.bottom="'Bullet List'"></button>
                        <button class="ql-indent" value="-1" v-tooltip.bottom="'Decrease Indent'"></button>
                        <button class="ql-indent" value="+1" v-tooltip.bottom="'Increase Indent'"></button>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-blockquote" v-tooltip.bottom="'Quote'"></button>
                        <button class="ql-code-block" v-tooltip.bottom="'Code Block'"></button>
                    </span>
                    <span class="ql-formats">
                        <button class="ql-link" v-tooltip.bottom="'Insert Link'"></button>
                        <button class="ql-clean" v-tooltip.bottom="'Remove Formatting'"></button>
                    </span>
                </template>
            </Editor>

            <div class="flex justify-between items-center mt-2">
                <small v-if="hasError" class="p-error">
                    {{ errorMessage }}
                </small>
                <small v-else class="text-surface-600 dark:text-surface-400"> Write detailed instructions for how your AI should interact with customers </small>

                <div class="flex items-center gap-2">
                    <div v-if="autoSaveStatus" class="flex items-center gap-1 text-xs">
                        <i
                            :class="[
                                autoSaveStatus === 'saving'
                                    ? 'pi pi-spin pi-spinner text-blue-500'
                                    : autoSaveStatus === 'saved'
                                      ? 'pi pi-check text-green-500'
                                      : autoSaveStatus === 'error'
                                        ? 'pi pi-exclamation-triangle text-red-500'
                                        : 'pi pi-circle text-surface-400'
                            ]"
                        ></i>
                        <span :class="[autoSaveStatus === 'saving' ? 'text-blue-600' : autoSaveStatus === 'saved' ? 'text-green-600' : autoSaveStatus === 'error' ? 'text-red-600' : 'text-surface-500']">
                            {{ autoSaveStatusText }}
                        </span>
                    </div>
                    <span class="text-xs text-surface-500"> {{ characterCount }} characters </span>
                </div>
            </div>
        </div>

        <!-- OpenAI Parameters Section -->
        <div class="mb-6">
            <OpenAiParametersConfig v-model="localParameters" :disabled="disabled || isLoading" @change="onParametersChange" />
        </div>

        <!-- Example Section -->
        <Card class="bg-surface-50 dark:bg-surface-800">
            <template #content>
                <div class="flex items-start gap-3">
                    <i class="pi pi-lightbulb text-yellow-500 mt-1"></i>
                    <div>
                        <h4 class="font-medium text-surface-900 dark:text-surface-0 mb-2">Example Chatbot Prompt</h4>
                        <div class="text-sm text-surface-700 dark:text-surface-300 bg-white dark:bg-surface-900 p-3 rounded border">
                            <p class="mb-2">"You are a helpful AI assistant for [Your Company Name], a [business type] company. You should be friendly, professional, and knowledgeable about our services.</p>
                            <p class="mb-2">Key information about our business: - We specialize in [your services] - Our business hours are [hours] - We serve customers in [location]</p>
                            <p>Always be helpful and try to qualify leads by asking about their specific needs. If you can't answer a question, politely direct them to contact our team directly."</p>
                        </div>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits } from 'vue';
import Card from 'primevue/card';
import Editor from 'primevue/editor';
import OpenAiParametersConfig from './OpenAIParametersConfig.vue';
import type { OpenAIParameters as OpenAIParametersType, ChatPromptConfig } from '../types';
import { DEFAULT_OPENAI_PARAMETERS } from '../types';

// Props
interface Props {
    modelValue: ChatPromptConfig;
    disabled?: boolean;
    autoSave?: boolean;
    tenantId?: string;
}

const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    autoSave: true
});

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: ChatPromptConfig];
    save: [value: ChatPromptConfig];
    validate: [isValid: boolean];
}>();

// Local state
const localPrompt = ref(props.modelValue.prompt || '');
const localParameters = ref<OpenAIParametersType>({
    temperature: props.modelValue.temperature ?? DEFAULT_OPENAI_PARAMETERS.temperature,
    frequency_penalty: props.modelValue.frequency_penalty ?? DEFAULT_OPENAI_PARAMETERS.frequency_penalty,
    presence_penalty: props.modelValue.presence_penalty ?? DEFAULT_OPENAI_PARAMETERS.presence_penalty,
    top_p: props.modelValue.top_p ?? DEFAULT_OPENAI_PARAMETERS.top_p
});

const isLoading = ref(false);
const autoSaveStatus = ref<'idle' | 'saving' | 'saved' | 'error'>('idle');
const errorMessage = ref('');

// Computed
const hasError = computed(() => !!errorMessage.value);
const characterCount = computed(() => {
    // Strip HTML tags for character count
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = localPrompt.value || '';
    return tempDiv.textContent?.length || 0;
});

const autoSaveStatusText = computed(() => {
    switch (autoSaveStatus.value) {
        case 'saving':
            return 'Saving...';
        case 'saved':
            return 'Saved';
        case 'error':
            return 'Save failed';
        default:
            return '';
    }
});

const promptPlaceholder = computed(() => `Start typing your chatbot instructions here. Use the formatting toolbar above to structure your prompt with headings, lists, and emphasis.`);

// Methods
const validatePrompt = (): boolean => {
    errorMessage.value = '';

    // Strip HTML tags for validation
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = localPrompt.value || '';
    const textContent = tempDiv.textContent || '';

    if (!textContent.trim()) {
        errorMessage.value = 'Chatbot prompt is required';
        return false;
    }

    if (textContent.length < 50) {
        errorMessage.value = 'Prompt should be at least 50 characters long';
        return false;
    }

    return true;
};

const emitUpdate = () => {
    const isValid = validatePrompt();
    const config: ChatPromptConfig = {
        prompt: localPrompt.value,
        ...localParameters.value
    };

    emit('update:modelValue', config);
    emit('validate', isValid);

    if (props.autoSave && isValid) {
        emit('save', config);
    }
};

const onPromptChange = () => {
    if (autoSaveStatus.value === 'error') {
        autoSaveStatus.value = 'idle';
    }
    emitUpdate();
};

const onPromptBlur = () => {
    validatePrompt();
};

const onParametersChange = () => {
    emitUpdate();
};

// Watch for external changes
watch(
    () => props.modelValue,
    (newValue) => {
        localPrompt.value = newValue.prompt || '';
        localParameters.value = {
            temperature: newValue.temperature ?? DEFAULT_OPENAI_PARAMETERS.temperature,
            frequency_penalty: newValue.frequency_penalty ?? DEFAULT_OPENAI_PARAMETERS.frequency_penalty,
            presence_penalty: newValue.presence_penalty ?? DEFAULT_OPENAI_PARAMETERS.presence_penalty,
            top_p: newValue.top_p ?? DEFAULT_OPENAI_PARAMETERS.top_p
        };
    },
    { deep: true }
);

// Expose methods for parent component
defineExpose({
    validate: validatePrompt,
    setAutoSaveStatus: (status: typeof autoSaveStatus.value) => {
        autoSaveStatus.value = status;
    }
});
</script>

<style scoped>
.chatbot-prompt-editor {
    @apply space-y-6;
}

:deep(.p-card-content) {
    @apply p-4;
}

:deep(.p-editor-content) {
    @apply text-sm;
}

:deep(.p-editor-toolbar) {
    @apply border-b border-surface-200 dark:border-surface-700;
}

:deep(.ql-editor) {
    @apply min-h-[250px];
}

:deep(.ql-toolbar) {
    @apply border-b border-surface-200 dark:border-surface-700;
}
</style>
