<script setup lang="ts">
import { defineProps, watch } from 'vue';

const props = defineProps<{
    success: boolean;
    error: string | null;
}>();
const emits = defineEmits<{
    statusSave: (status: boolean) => void;
    reset: [];
}>();
</script>

<template>
    <div class="result-container" :class="{ 'success-container': success, 'error-container': !success }">
        <div class="result-icon-container">
            <i v-if="success" class="pi pi-check-circle success-icon"></i>
            <i v-else class="pi pi-times-circle error-icon"></i>
        </div>

        <div class="result-content">
            <h2 v-if="success" class="result-title success-title">Appointment Booked Successfully!</h2>
            <h2 v-else class="result-title error-title">Booking Failed</h2>

            <div v-if="success" class="success-message">
                <p>Your appointment has been confirmed. You will receive a confirmation email shortly with all the details.</p>
            </div>

            <div v-else class="error-message">
                <p>We encountered a problem while booking your appointment:</p>
                <div class="error-details">{{ error }}</div>
            </div>

            <div class="action-buttons">
                <Button v-if="success" label="Booking Complete" icon="pi pi-home" class="p-button-success" @click="emits('reset')" />

                <Button v-else label="Try Again" icon="pi pi-refresh" class="p-button-danger" @click="emits('reset')" />
            </div>
        </div>
    </div>
</template>

<style scoped>
.result-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2.5rem 2rem;
    border-radius: 1rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--surface-card);
    position: relative;
    overflow: hidden;
}

.success-container {
    border-top: 5px solid var(--green-500);
}

.error-container {
    border-top: 5px solid var(--red-500);
}

.result-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(90deg, var(--success-container ? '--green-300': '--red-300'), var(--success-container ? '--green-600': '--red-600'));
    opacity: 0.7;
}

.result-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1.5rem;
}

.success-container .result-icon-container {
    background-color: rgba(var(--green-500-rgb), 0.1);
}

.error-container .result-icon-container {
    background-color: rgba(var(--red-500-rgb), 0.1);
}

.success-icon {
    font-size: 3rem;
    color: var(--green-500);
}

.error-icon {
    font-size: 3rem;
    color: var(--red-500);
}

.result-content {
    width: 100%;
}

.result-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    letter-spacing: -0.5px;
}

.success-title {
    color: var(--green-600);
}

.error-title {
    color: var(--red-600);
}

.success-message,
.error-message {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color-secondary);
}

.error-details {
    margin-top: 1rem;
    padding: 1rem;
    background-color: rgba(var(--red-500-rgb), 0.05);
    border-radius: 0.5rem;
    color: var(--red-700);
    font-weight: 500;
    border-left: 4px solid var(--red-500);
}

.action-buttons {
    margin-top: 1.5rem;
}

/* Dark mode adjustments */
:root[class*='app-dark'] .success-container {
    border-top-color: var(--green-400);
}

:root[class*='app-dark'] .error-container {
    border-top-color: var(--red-400);
}

:root[class*='app-dark'] .success-title {
    color: var(--green-400);
}

:root[class*='app-dark'] .error-title {
    color: var(--red-400);
}

:root[class*='app-dark'] .error-details {
    background-color: rgba(var(--red-500-rgb), 0.1);
    color: var(--red-400);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .result-container {
        padding: 2rem 1.5rem;
    }

    .result-icon-container {
        width: 70px;
        height: 70px;
    }

    .success-icon,
    .error-icon {
        font-size: 2.5rem;
    }

    .result-title {
        font-size: 1.5rem;
    }
}

/* Animation for success state */
@keyframes successPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.success-container .result-icon-container {
    animation: successPulse 2s ease-in-out infinite;
}
</style>
