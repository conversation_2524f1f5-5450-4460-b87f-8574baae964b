<template>
    <div class="onboarding-header">
        <div class="header-content">
            <!-- Icon Section -->
            <div v-if="icon" class="header-icon">
                <div class="icon-container">
                    <i :class="icon" class="feature-icon"></i>
                </div>
            </div>

            <!-- Text Content -->
            <div class="header-text">
                <h1 class="header-title">{{ title }}</h1>
                <p v-if="description" class="header-description">{{ description }}</p>
            </div>

            <!-- Action Buttons -->
            <div v-if="showActions" class="header-actions">
                <slot name="actions">
                    <Button
                        v-if="showBackButton"
                        icon="pi pi-arrow-left"
                        severity="secondary"
                        outlined
                        @click="$emit('back')"
                        :disabled="disabled"
                        aria-label="Go back"
                    />
                    <Button
                        v-if="showCloseButton"
                        icon="pi pi-times"
                        severity="secondary"
                        text
                        @click="$emit('close')"
                        :disabled="disabled"
                        aria-label="Close"
                    />
                </slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Button from 'primevue/button';

// Props
interface Props {
    title: string;
    description?: string;
    icon?: string;
    showActions?: boolean;
    showBackButton?: boolean;
    showCloseButton?: boolean;
    disabled?: boolean;
    centered?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    showActions: false,
    showBackButton: false,
    showCloseButton: false,
    disabled: false,
    centered: true
});

// Emits
const emit = defineEmits<{
    back: [];
    close: [];
}>();
</script>

<style scoped>
.onboarding-header {
    @apply mb-8;
}

.header-content {
    @apply flex flex-col items-center text-center gap-6;
    @apply lg:flex-row lg:text-left lg:justify-between;
}

.header-icon {
    @apply flex-shrink-0;
}

.icon-container {
    @apply w-20 h-20 rounded-2xl bg-gradient-to-br from-primary-500 to-primary-600;
    @apply flex items-center justify-center shadow-lg;
}

.feature-icon {
    @apply text-4xl text-white;
}

.header-text {
    @apply flex-1 min-w-0;
}

.header-title {
    @apply text-3xl font-bold text-surface-900 dark:text-surface-0 mb-3;
    @apply lg:text-4xl;
}

.header-description {
    @apply text-lg text-surface-600 dark:text-surface-400 leading-relaxed;
    @apply max-w-2xl mx-auto lg:mx-0;
}

.header-actions {
    @apply flex items-center gap-3 flex-shrink-0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .header-content {
        @apply text-center;
    }
    
    .header-text {
        @apply text-center;
    }
    
    .header-description {
        @apply mx-auto;
    }
}

/* Centered variant */
.onboarding-header:has(.header-content.centered) .header-content {
    @apply flex-col items-center text-center;
}

.onboarding-header:has(.header-content.centered) .header-description {
    @apply mx-auto;
}
</style>
