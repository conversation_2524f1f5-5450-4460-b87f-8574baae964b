import type { Timestamp } from 'firebase/firestore';
import { CompanyInfo } from '@/features/company-info-update';
import type { LeadActionsConfig, LeadSourcesConfig, LeadStatusesConfig } from 'src/features/lead-configuration-onboarding';
import { EmailConfig } from '@/features/email-onboarding/types';
import { CalendarConfig } from '@/features/calendar-onboarding/types';
import type { TenantPromptConfigs } from '@/features/prompts-onboarding/types';
import { ScriptsCollection } from '@/features/scripts-onboarding';

// Communication service configuration
export interface CommunicationService {
    defaultNumber?: string;
    numbers?: string[];
    validated?: boolean;
}

// Defines the structure for a Tenant object
export interface Tenant extends CompanyInfo, TenantPromptConfigs {
    id: string; // Unique identifier for the tenant

    // Core company details
    name: string;
    emailAddress: string;
    phone: string;
    address: string;
    website?: string;
    ownerId: string; // ID of the user who owns this tenant record
    approved: boolean; // Whether the tenant has been approved
    createdAt: Timestamp | Date; // Timestamp of when the tenant was created
    updatedAt: Timestamp | Date; // Timestamp of the last update

    // Email configuration for chatbot integration
    email?: EmailConfig;

    // Calendar configuration for appointment integration
    calendar?: CalendarConfig;

    // Communication services
    sms?: CommunicationService;
    call?: CommunicationService;
    ai_call?: CommunicationService;

    // Lead Configuration
    lead_actions?: LeadActionsConfig;
    lead_sources?: LeadSourcesConfig;
    lead_statuses?: LeadStatusesConfig;

    // Scripts
    scripts?: ScriptsCollection;
}
