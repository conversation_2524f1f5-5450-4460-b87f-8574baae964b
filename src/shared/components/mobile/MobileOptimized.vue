<template>
    <div 
        class="mobile-optimized"
        :class="mobileClasses"
    >
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

// Props
interface Props {
    touchFriendly?: boolean;
    swipeEnabled?: boolean;
    autoHideNavigation?: boolean;
    fullHeight?: boolean;
    safeArea?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    touchFriendly: true,
    swipeEnabled: false,
    autoHideNavigation: false,
    fullHeight: false,
    safeArea: true
});

// Emits
const emit = defineEmits<{
    swipeLeft: [];
    swipeRight: [];
    swipeUp: [];
    swipeDown: [];
}>();

// Reactive state
const isMobile = ref(false);
const isLandscape = ref(false);
const touchStart = ref<{ x: number; y: number } | null>(null);

// Computed
const mobileClasses = computed(() => {
    return {
        'mobile-touch-friendly': props.touchFriendly,
        'mobile-swipe-enabled': props.swipeEnabled,
        'mobile-full-height': props.fullHeight,
        'mobile-safe-area': props.safeArea,
        'mobile-landscape': isLandscape.value,
        'is-mobile': isMobile.value
    };
});

// Methods
const checkMobile = () => {
    isMobile.value = window.innerWidth < 768;
    isLandscape.value = window.innerWidth > window.innerHeight;
};

const handleTouchStart = (event: TouchEvent) => {
    if (!props.swipeEnabled) return;
    
    const touch = event.touches[0];
    touchStart.value = {
        x: touch.clientX,
        y: touch.clientY
    };
};

const handleTouchEnd = (event: TouchEvent) => {
    if (!props.swipeEnabled || !touchStart.value) return;
    
    const touch = event.changedTouches[0];
    const deltaX = touch.clientX - touchStart.value.x;
    const deltaY = touch.clientY - touchStart.value.y;
    
    const minSwipeDistance = 50;
    
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // Horizontal swipe
        if (Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                emit('swipeRight');
            } else {
                emit('swipeLeft');
            }
        }
    } else {
        // Vertical swipe
        if (Math.abs(deltaY) > minSwipeDistance) {
            if (deltaY > 0) {
                emit('swipeDown');
            } else {
                emit('swipeUp');
            }
        }
    }
    
    touchStart.value = null;
};

const handleResize = () => {
    checkMobile();
};

// Lifecycle
onMounted(() => {
    checkMobile();
    window.addEventListener('resize', handleResize);
    
    if (props.swipeEnabled) {
        document.addEventListener('touchstart', handleTouchStart, { passive: true });
        document.addEventListener('touchend', handleTouchEnd, { passive: true });
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    
    if (props.swipeEnabled) {
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('touchend', handleTouchEnd);
    }
});
</script>

<style scoped>
.mobile-optimized {
    width: 100%;
    position: relative;
}

/* Touch-friendly sizing */
.mobile-touch-friendly {
    --min-touch-target: 44px;
}

.mobile-touch-friendly :deep(.p-button),
.mobile-touch-friendly :deep(.p-inputtext),
.mobile-touch-friendly :deep(.p-dropdown) {
    min-height: var(--min-touch-target);
}

.mobile-touch-friendly :deep(.p-button) {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Swipe enabled styling */
.mobile-swipe-enabled {
    touch-action: pan-y;
    user-select: none;
}

/* Full height on mobile */
.mobile-full-height.is-mobile {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
}

/* Safe area support for devices with notches */
.mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
}

/* Landscape mode adjustments */
.mobile-landscape.is-mobile {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* Mobile-specific improvements */
.is-mobile {
    /* Improve scrolling on iOS */
    -webkit-overflow-scrolling: touch;
    
    /* Prevent zoom on input focus */
    font-size: 16px;
}

.is-mobile :deep(.p-inputtext),
.is-mobile :deep(.p-textarea),
.is-mobile :deep(.p-dropdown) {
    font-size: 16px; /* Prevent zoom on iOS */
}

/* Improve tap targets */
.is-mobile :deep(a),
.is-mobile :deep(button),
.is-mobile :deep(.p-button) {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Better spacing for mobile */
.is-mobile :deep(.space-y-6 > * + *) {
    margin-top: 1rem;
}

.is-mobile :deep(.gap-3) {
    gap: 0.75rem;
}

.is-mobile :deep(.gap-4) {
    gap: 1rem;
}

.is-mobile :deep(.gap-6) {
    gap: 1.5rem;
}

/* Mobile card improvements */
.is-mobile :deep(.p-card) {
    border-radius: 0.75rem;
    margin: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Mobile modal improvements */
.is-mobile :deep(.p-dialog) {
    width: 95vw !important;
    max-width: 95vw !important;
    margin: 1rem !important;
    border-radius: 1rem !important;
}

.is-mobile :deep(.p-dialog .p-dialog-content) {
    padding: 1rem !important;
}

.is-mobile :deep(.p-dialog .p-dialog-header) {
    padding: 1rem !important;
    border-radius: 1rem 1rem 0 0 !important;
}

/* Mobile navigation improvements */
.is-mobile :deep(.flex.justify-between) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
}

.is-mobile :deep(.flex.justify-between .p-button) {
    width: 100%;
}

/* Mobile form improvements */
.is-mobile :deep(.grid) {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
}

.is-mobile :deep(.flex.gap-2),
.is-mobile :deep(.flex.gap-3),
.is-mobile :deep(.flex.gap-4) {
    flex-direction: column;
    width: 100%;
}

.is-mobile :deep(.flex.gap-2 .p-button),
.is-mobile :deep(.flex.gap-3 .p-button),
.is-mobile :deep(.flex.gap-4 .p-button) {
    width: 100%;
}

/* Improve readability on mobile */
.is-mobile :deep(h1) {
    font-size: 1.875rem;
    line-height: 1.2;
}

.is-mobile :deep(h2) {
    font-size: 1.5rem;
    line-height: 1.3;
}

.is-mobile :deep(h3) {
    font-size: 1.25rem;
    line-height: 1.4;
}

.is-mobile :deep(p) {
    line-height: 1.6;
}

/* Dark mode improvements for mobile */
@media (prefers-color-scheme: dark) {
    .is-mobile :deep(.p-card) {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .mobile-optimized :deep(*) {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
</style>
