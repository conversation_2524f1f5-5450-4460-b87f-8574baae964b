<template>
    <div class="onboarding-setup">
        <!-- Welcome Screen -->
        <div v-if="showWelcome" class="welcome-screen">
            <OnboardingLayout background="surface" max-width="xl">
                <template #header>
                    <OnboardingHeader
                        title="Welcome to Your Liftt Platform Setup"
                        description="Let's get your business up and running with our comprehensive onboarding process. We'll guide you through setting up all the essential features for your success."
                        icon="pi pi-sparkles"
                        :centered="true"
                    />
                </template>

                <div class="welcome-content">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <!-- Setup Overview -->
                        <Card class="setup-overview">
                            <template #header>
                                <div class="card-header">
                                    <i class="pi pi-list-check text-3xl text-primary mb-3"></i>
                                    <h3 class="text-xl font-semibold">What We'll Set Up</h3>
                                </div>
                            </template>
                            <template #content>
                                <div class="space-y-3">
                                    <div v-for="category in featureCategories" :key="category.id" class="category-item">
                                        <div class="flex items-center gap-3">
                                            <i :class="[category.icon, category.color]" class="text-lg"></i>
                                            <div>
                                                <div class="font-medium">{{ category.title }}</div>
                                                <div class="text-sm text-surface-600 dark:text-surface-400">
                                                    {{ category.description }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </Card>

                        <!-- Time Estimate -->
                        <Card class="time-estimate">
                            <template #header>
                                <div class="card-header">
                                    <i class="pi pi-clock text-3xl text-green-500 mb-3"></i>
                                    <h3 class="text-xl font-semibold">Time Investment</h3>
                                </div>
                            </template>
                            <template #content>
                                <div class="space-y-4">
                                    <div class="text-center">
                                        <div class="text-3xl font-bold text-primary">{{ totalEstimatedTime }}</div>
                                        <div class="text-sm text-surface-600 dark:text-surface-400">Total estimated time</div>
                                    </div>
                                    <Divider />
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-sm">
                                            <span>Essential Setup</span>
                                            <span class="font-medium">8 minutes</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span>Communication</span>
                                            <span class="font-medium">22 minutes</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span>AI & Automation</span>
                                            <span class="font-medium">27 minutes</span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span>Configuration</span>
                                            <span class="font-medium">8 minutes</span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button label="Start Full Setup" icon="pi pi-play" size="large" class="px-8 py-3" @click="startFullOnboarding" />
                        <Button label="Choose Features" icon="pi pi-list" severity="secondary" outlined size="large" class="px-8 py-3" @click="showFeatureSelection" />
                        <Button label="Skip Setup" icon="pi pi-arrow-right" severity="secondary" text size="large" @click="skipOnboarding" />
                    </div>
                </div>
            </OnboardingLayout>
        </div>

        <!-- Feature Selection Screen -->
        <div v-else-if="showSelection" class="feature-selection-screen">
            <FeatureSelection
                :features="availableFeatures"
                :categories="featureCategories"
                @features-selected="handleFeaturesSelected"
                @back="
                    showWelcome = true;
                    showSelection = false;
                "
            />
        </div>

        <!-- Main Onboarding Process -->
        <div v-else-if="onboardingService.isActive" class="onboarding-process">
            <OnboardingLayout background="default" max-width="xl">
                <template #header>
                    <div class="process-header">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center gap-4">
                                <Button v-if="onboardingService.previousFeature" icon="pi pi-arrow-left" severity="secondary" outlined @click="goToPreviousFeature" :disabled="onboardingService.currentState.isLoading" />
                                <div>
                                    <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
                                        {{ currentFeature?.title }}
                                    </h1>
                                    <p class="text-surface-600 dark:text-surface-400">
                                        {{ currentFeature?.description }}
                                    </p>
                                </div>
                            </div>
                            <Button icon="pi pi-times" severity="secondary" text @click="exitOnboarding" v-tooltip.left="'Exit setup'" />
                        </div>
                    </div>
                </template>

                <template #steps>
                    <ProgressTracking :progress="onboardingService.progress" :current-feature="currentFeature" :features="onboardingService.currentState.features" @step-click="handleStepClick" @toggle-view="handleProgressViewToggle" />
                </template>

                <!-- Dynamic Feature Component -->
                <div class="feature-container">
                    <component :is="currentFeatureComponent" v-if="currentFeatureComponent && currentFeature" v-bind="getFeatureProps(currentFeature.id)" v-on="getFeatureEventHandlers(currentFeature.id)" :key="currentFeature.id" />
                </div>

                <template #footer>
                    <div class="onboarding-actions">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <Button
                                    v-if="currentFeature && !currentFeature.isRequired"
                                    label="Skip This Step"
                                    icon="pi pi-arrow-right"
                                    severity="secondary"
                                    outlined
                                    @click="skipCurrentFeature"
                                    :disabled="onboardingService.currentState.isLoading"
                                />
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="text-sm text-surface-600 dark:text-surface-400 mx-5"> {{ onboardingService.progress.estimatedTimeRemaining }} remaining </span>
                                <Button v-if="onboardingService.nextFeature" label="Continue" icon="pi pi-arrow-right" iconPos="right" @click="goToNextFeature" :disabled="!canProceed || onboardingService.currentState.isLoading" />
                            </div>
                        </div>
                    </div>
                </template>
            </OnboardingLayout>
        </div>

        <!-- Completion Screen -->
        <div v-else-if="showCompletion" class="completion-screen">
            <OnboardingLayout background="surface" max-width="lg">
                <template #header>
                    <OnboardingHeader title="🎉 Setup Complete!" description="Congratulations! You've successfully configured your SaaS platform. You're now ready to start managing leads and growing your business." :centered="true" />
                </template>

                <div class="completion-content">
                    <Card class="completion-summary">
                        <template #content>
                            <div class="text-center space-y-6">
                                <div class="completion-stats">
                                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
                                        <div class="stat-item">
                                            <div class="text-2xl font-bold text-green-500">
                                                {{ onboardingService.progress.completedFeatures }}
                                            </div>
                                            <div class="text-sm text-surface-600 dark:text-surface-400">Features Completed</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="text-2xl font-bold text-primary">{{ Math.round((Date.now() - onboardingService.progress.startedAt.getTime()) / 60000) }}m</div>
                                            <div class="text-sm text-surface-600 dark:text-surface-400">Time Spent</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="text-2xl font-bold text-blue-500">100%</div>
                                            <div class="text-sm text-surface-600 dark:text-surface-400">Setup Progress</div>
                                        </div>
                                    </div>
                                </div>

                                <Divider />

                                <div class="next-steps">
                                    <h3 class="text-lg font-semibold mb-4">What's Next?</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <Button label="Go to Dashboard" icon="pi pi-home" size="large" class="w-full" @click="goToDashboard" />
                                        <Button label="View Setup Summary" icon="pi pi-list" severity="secondary" outlined size="large" class="w-full" @click="showSetupSummary" />
                                    </div>
                                </div>
                            </div>
                        </template>
                    </Card>
                </div>
            </OnboardingLayout>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'primevue/usetoast';

// PrimeVue Components
import Card from 'primevue/card';
import Button from 'primevue/button';
import Divider from 'primevue/divider';

// Shared Components
import { OnboardingLayout, OnboardingHeader } from '@/shared/components/onboarding';

// Feature Components
import FeatureSelection from './components/FeatureSelection.vue';
import ProgressTracking from './components/ProgressTracking.vue';

// Services
import { OnboardingService } from './services/OnboardingService';
import { FeatureIntegrationService } from './services/FeatureIntegrationService';

// Constants and Types
import { FEATURE_CATEGORIES, ONBOARDING_FEATURES, FEATURE_ORDER } from './constants';
import type { OnboardingFeature } from './types';

// Props
interface Props {
    autoStart?: boolean;
    selectedFeatures?: string[];
    redirectOnComplete?: string;
}

const props = withDefaults(defineProps<Props>(), {
    autoStart: false,
    redirectOnComplete: '/'
});

// Emits
const emit = defineEmits<{
    complete: [data: any];
    exit: [];
    featureComplete: [featureId: string, data: any];
}>();

// Composables
const router = useRouter();
const toast = useToast();

// Services
const onboardingService = new OnboardingService({
    redirectOnComplete: props.redirectOnComplete,
    onComplete: () => handleOnboardingComplete(),
    onFeatureComplete: (featureId, data) => handleFeatureComplete(featureId, data),
    onError: (error, featureId) => handleError(error, featureId)
});

const integrationService = new FeatureIntegrationService();

// State
const showWelcome = ref(!props.autoStart);
const showSelection = ref(false);
const showCompletion = ref(false);

// Computed
const featureCategories = computed(() => Object.values(FEATURE_CATEGORIES));
const availableFeatures = computed(() => Object.values(ONBOARDING_FEATURES));
const currentFeature = computed(() => onboardingService.currentFeature);
const canProceed = computed(() => onboardingService.canProceed);

const totalEstimatedTime = computed(() => {
    const totalMinutes = Object.values(ONBOARDING_FEATURES).reduce((total, feature) => {
        const minutes = parseInt(feature.estimatedTime.match(/\d+/)?.[0] || '0');
        return total + minutes;
    }, 0);

    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return hours > 0 ? `${hours}h ${minutes}m` : `${totalMinutes}m`;
});

const currentFeatureComponent = computed(() => {
    if (!currentFeature.value) return null;
    return integrationService.getFeatureComponent(currentFeature.value.id);
});

// Methods
const startFullOnboarding = async () => {
    showWelcome.value = false;
    await onboardingService.startOnboarding(FEATURE_ORDER);
};

const showFeatureSelection = () => {
    showWelcome.value = false;
    showSelection.value = true;
};

const handleFeaturesSelected = async (selectedFeatures: string[]) => {
    showSelection.value = false;
    await onboardingService.startOnboarding(selectedFeatures);
};

const skipOnboarding = () => {
    emit('exit');
    router.push(props.redirectOnComplete);
};

const exitOnboarding = () => {
    emit('exit');
    showWelcome.value = true;
    onboardingService.reset();
};

const goToPreviousFeature = async () => {
    await onboardingService.previousStep();
};

const goToNextFeature = async () => {
    await onboardingService.nextStep();
};

const skipCurrentFeature = async () => {
    if (currentFeature.value) {
        await onboardingService.skipFeature(currentFeature.value.id);
    }
};

const getFeatureProps = (featureId: string) => {
    return integrationService.getFeatureProps(featureId);
};

const getFeatureEventHandlers = (featureId: string) => {
    return integrationService.getFeatureEventHandlers(
        featureId,
        (data) => handleFeatureComplete(featureId, data),
        (error) => handleError(error, featureId)
    );
};

const handleFeatureComplete = async (featureId: string, data: any) => {
    await onboardingService.completeFeature(featureId, data);
    emit('featureComplete', featureId, data);

    toast.add({
        severity: 'success',
        summary: 'Feature Completed',
        detail: `${ONBOARDING_FEATURES[featureId]?.title} has been set up successfully`,
        life: 3000
    });
};

const handleError = (error: string, featureId?: string) => {
    onboardingService.handleError(error, featureId);

    toast.add({
        severity: 'error',
        summary: 'Setup Error',
        detail: error,
        life: 5000
    });
};

const handleOnboardingComplete = () => {
    showCompletion.value = true;
    emit('complete', {
        completedFeatures: onboardingService.currentState.completedFeatures,
        progress: onboardingService.progress
    });
};

const goToDashboard = () => {
    router.push(props.redirectOnComplete);
};

const showSetupSummary = () => {
    // Could open a modal or navigate to summary page
    console.log('Show setup summary');
};

const handleStepClick = async (feature: OnboardingFeature) => {
    // Navigate to the clicked feature
    await onboardingService.goToFeature(feature.id);
};

const handleProgressViewToggle = (isFullView: boolean) => {
    console.log('Progress view toggled:', isFullView ? 'Full View' : 'Compact View');
};

// Lifecycle
onMounted(() => {
    // Debug: Log component mappings
    console.log('Onboarding Setup - Component Mappings:', integrationService.getComponentMapping());
    console.log('Onboarding Setup - Available Components:', Object.keys(integrationService.getAllComponents()));

    if (props.autoStart && props.selectedFeatures) {
        onboardingService.startOnboarding(props.selectedFeatures);
    }
});

onUnmounted(() => {
    // Cleanup if needed
});
</script>

<style scoped>
.onboarding-setup {
    min-height: 100vh;
}

/* Welcome Screen */
.welcome-content {
    margin: 0 auto;
}

.card-header {
    text-align: center;
    padding: 1.5rem;
}

.category-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--surface-border);
}

.category-item:last-child {
    border-bottom: none;
}

/* Process Header */
.process-header {
    background: var(--surface-0);
    border-bottom: 1px solid var(--surface-border);
    margin: -2rem -2rem 2rem -2rem;
    padding: 2rem;
}

/* Feature Container */
.feature-container {
    min-height: 400px;
    margin: 2rem 0;
}

/* Onboarding Actions */
.onboarding-actions {
    background: var(--surface-50);
    border-top: 1px solid var(--surface-border);
    margin: 2rem -2rem -2rem -2rem;
    padding: 1.5rem 2rem;
}

/* Completion Screen */
.completion-content {
    max-width: 600px;
    margin: 0 auto;
}

.completion-stats {
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
}

.next-steps {
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .process-header {
        margin: -1rem -1rem 1rem -1rem;
        padding: 1rem;
    }

    .onboarding-actions {
        margin: 1rem -1rem -1rem -1rem;
        padding: 1rem;
    }

    .onboarding-actions .flex {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .feature-container {
        margin: 1rem 0;
        min-height: 300px;
    }
}

/* Dark mode adjustments */
.dark .process-header {
    background: var(--surface-800);
}

.dark .onboarding-actions {
    background: var(--surface-800);
}
</style>
